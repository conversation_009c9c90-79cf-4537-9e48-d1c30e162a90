import React from 'react'
const dashboard = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M27.204 15.15C27.0529 15.15 26.7506 15.3 26.5995 15.45L25.0882 16.95L28.262 20.1L29.7733 18.6C30.0756 18.3 30.0756 17.7 29.7733 17.4L27.8086 15.45C27.6574 15.3 27.5063 15.15 27.204 15.15ZM24.3325 17.85L15.1133 26.85V30H18.2872L27.5063 20.85L24.3325 17.85ZM27.204 9H15.1133V0H27.204V9ZM15.1133 22.59V12H27.204V12.15C26.0554 12.15 25.0428 12.75 24.4685 13.335L15.1133 22.59ZM12.0907 15H0V0H12.0907V15ZM12.0907 27H0V18H12.0907V27Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}

function Dashboard({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M27.204 15.15C27.0529 15.15 26.7506 15.3 26.5995 15.45L25.0882 16.95L28.262 20.1L29.7733 18.6C30.0756 18.3 30.0756 17.7 29.7733 17.4L27.8086 15.45C27.6574 15.3 27.5063 15.15 27.204 15.15ZM24.3325 17.85L15.1133 26.85V30H18.2872L27.5063 20.85L24.3325 17.85ZM27.204 9H15.1133V0H27.204V9ZM15.1133 22.59V12H27.204V12.15C26.0554 12.15 25.0428 12.75 24.4685 13.335L15.1133 22.59ZM12.0907 15H0V0H12.0907V15ZM12.0907 27H0V18H12.0907V27Z" fill={color} />
            </svg>
    </div>
    <div className="hidden md:flex">
        {dashboard(color,h,w,txt)}
    </div>
    </>
  )
}

export default Dashboard