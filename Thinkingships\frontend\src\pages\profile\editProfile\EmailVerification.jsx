import React, { useEffect, useState } from 'react';
import { FiLoader } from 'react-icons/fi'; // Spinner icon
import TokenHandling from '../../../action/TokenHandling';
import toast from 'react-hot-toast';

function EmailVerification({ email }) {
  const [counter, setCounter] = useState(5); // 2 minutes
  const [loading, setLoading] = useState(false);
  const {resendTokenReq,tokenLoading,result} = TokenHandling();

  // countdown timer logic
  useEffect(()=>{
    toast('Verification Email send.', {
        icon: '📬',
        });
  },[])

  useEffect(() => {
    if (counter === 0) return;
    const interval = setInterval(() => setCounter(prev => prev - 1), 1000);
    return () => clearInterval(interval);
  }, [counter]);

  const handleResend = async() => {
    
    try {
      const result = await resendTokenReq();
      // const result = true;
      if(result){
        toast('Email send.', {
        icon: '📬',
        });
        setCounter(5);
      }

    } catch (err) {
      console.error('Resend failed:', err);
    }
  };

  return (
    <div className="max-w-full mx-auto mt-10 p-6 shadow-md  rounded-lg bg-white flex-1">
      <h2 className="text-xl font-semibold mb-4">Verify Your Email</h2>
      <p className="text-gray-600 mb-2">We’ve sent a verification link to:</p>
      <div className="bg-gray-100 p-3 rounded text-sm font-medium text-gray-700 mb-4">{email}</div>

      <button
        disabled={counter > 0 || tokenLoading}
        onClick={handleResend}
        className={`w-full py-2 px-4 rounded-md text-white font-semibold transition ${
          tokenLoading || counter > 0 ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#4A99F8] hover:bg-[#3e87dd]'
        }`}
      >
        {tokenLoading ? (
          <div className="flex justify-center items-center gap-2">
            <FiLoader className="animate-spin" /> Sending...
          </div>
        ) : (
          'Resend Verification Link'
        )}
      </button>

      {counter > 0 && (
        <p className="text-sm text-gray-500 text-center mt-2">
          You can resend the link in <strong>{Math.floor(counter / 60)}:{String(counter % 60).padStart(2, '0')}</strong>
        </p>
      )}
    </div>
  );
}

export default EmailVerification;
