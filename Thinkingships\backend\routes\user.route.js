const express = require('express');
const { createUser, loginUser, verifyEmail, fetchMe, logout, resendToken, fetchProfile, updateProfile, showProfile } = require('../controller/user.controller');
const { isAuthenticated } = require('../middleware/authentication');
const router = express.Router();

router.route(`/user/create/:userRole`).post(createUser);
router.route(`/user/login`).post(loginUser)
router.route(`/user/me`).get(isAuthenticated,fetchMe)
router.route(`/user/logout`).get(isAuthenticated,logout)

router.route(`/user/fetchProfile`).get(isAuthenticated,fetchProfile)
router.route(`/user/updateProfile`).put(isAuthenticated,updateProfile)

router.route(`/user/verify-email/:token`).put(verifyEmail)
router.route(`/user/resend-token`).put(isAuthenticated,resendToken)

router.route('/user/profile/:userId').get(isAuthenticated,showProfile);

module.exports = router;