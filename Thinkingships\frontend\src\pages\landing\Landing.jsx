import LandingNavbar from './LandingNavbar'
import Icon from '/landing/Icon.svg'
import author from '/landing/author-new.png.png'
import publication from '/landing/publication.png.png'
import business from '/landing/business.png.png'
import '../../styles/animations.css'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'

function Landing () {
  const navigate = useNavigate()

  return (
    <div className='bg-white min-h-screen'>
      <LandingNavbar />

      {/* Hero Section */}
      <motion.div
        className='bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden'
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5 }}
      >
        {/* Animated Background Elements */}
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            background: [
              "radial-gradient(circle at 20% 50%, #1e40af 0%, transparent 50%)",
              "radial-gradient(circle at 80% 20%, #3730a3 0%, transparent 50%)",
              "radial-gradient(circle at 40% 80%, #1e3a8a 0%, transparent 50%)",
              "radial-gradient(circle at 20% 50%, #1e40af 0%, transparent 50%)"
            ]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating Bubble Particles */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-white/30 rounded-full border-2 border-white/50 shadow-lg"
            style={{
              width: `${Math.random() * 30 + 20}px`,
              height: `${Math.random() * 30 + 20}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              backdropFilter: 'blur(10px)',
              boxShadow: '0 4px 20px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
            }}
            animate={{
              y: [0, -100, -200],
              x: [0, Math.random() * 50 - 25, Math.random() * 100 - 50],
              opacity: [0, 0.8, 0],
              scale: [0.5, 1, 1.2],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeOut"
            }}
          />
        ))}

        {/* Small Bubble Particles */}
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={`small-${i}`}
            className="absolute bg-blue-300/20 rounded-full"
            style={{
              width: `${Math.random() * 8 + 4}px`,
              height: `${Math.random() * 8 + 4}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}


            
            animate={{
              y: [0, -150],
              x: [0, Math.random() * 30 - 15],
              opacity: [0, 0.8, 0],
              scale: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 6 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 4,
              ease: "linear"
            }}
          />
        ))}

        {/* Large Floating Bubbles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`large-${i}`}
            className="absolute bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full border border-white/10"
            style={{
              width: `${Math.random() * 40 + 30}px`,
              height: `${Math.random() * 40 + 30}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -80, -160],
              x: [0, Math.random() * 40 - 20, Math.random() * 60 - 30],
              opacity: [0, 0.4, 0],
              scale: [0.8, 1.1, 1.3],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 12 + Math.random() * 6,
              repeat: Infinity,
              delay: Math.random() * 8,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Glowing Orbs */}
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-24 h-24 bg-purple-500/10 rounded-full blur-xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center'>
            {/* Left Section - Text */}
            <motion.div
              className='order-2 lg:order-1'
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
            >
              <motion.h1
                className='text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight'
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1.2, delay: 0.3, ease: "easeOut" }}
                whileHover={{ scale: 1.05 }}
                style={{
                  textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.3)'
                }}
              >
                Read Write Connect
                <motion.span
                  animate={{ opacity: [0, 1, 1, 0, 0, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    times: [0, 0.2, 0.4, 0.6, 0.8, 1]
                  }}
                >
                  .
                </motion.span>
                <motion.span
                  animate={{ opacity: [0, 0, 1, 1, 0, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    times: [0, 0.2, 0.4, 0.6, 0.8, 1]
                  }}
                >
                  .
                </motion.span>
                <motion.span
                  animate={{ opacity: [0, 0, 0, 1, 1, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    times: [0, 0.2, 0.4, 0.6, 0.8, 1]
                  }}
                >
                  .
                </motion.span>
              </motion.h1>
              <motion.div
                className='space-y-4 mb-8'
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <p className='text-lg text-gray-200 leading-relaxed'>
                  Join India's storytelling platform for Authors, Publications & Businesses.
                </p>
                <p className='text-lg text-gray-200 leading-relaxed'>
                  A home for stories that matter.
                </p>
                <p className='text-lg text-gray-200 leading-relaxed'>
                  Whether you write them, publish them or amplify them — Skrivee helps you do it better.
                </p>
              </motion.div>
              <motion.button
                className='bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-md transition duration-300 text-lg shadow-lg'
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                whileHover={{
                  scale: 1.1,
                  boxShadow: "0 20px 40px rgba(59, 130, 246, 0.6)",
                  y: -5
                }}
                whileTap={{ scale: 0.95 }}
              >
                Start Skriving
              </motion.button>
            </motion.div>

            {/* Right Section - Image */}
            <motion.div
              className='order-1 lg:order-2 flex justify-center'
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            >
              <motion.img
                src={Icon}
                alt='Skrivee Icon'
                className='w-full max-w-md lg:max-w-lg h-auto'
                initial={{ scale: 0.8, rotate: -10, y: 0 }}
                animate={{
                  scale: 1,
                  rotate: 0,
                  y: [0, -10, 0]
                }}
                transition={{
                  duration: 1.5,
                  delay: 0.7,
                  ease: "easeOut",
                  y: {
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }
                }}
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  transition: { duration: 0.3 }
                }}
              />
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Divider Section */}
      <motion.div
        className='bg-blue-50 py-8'
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-center'>
            <div className='flex-1 h-1 bg-gradient-to-r from-transparent via-blue-400 to-blue-500'></div>
            <div className='px-8'>
              <span className='text-lg font-medium text-gray-700 whitespace-nowrap hover:text-blue-600 transition-colors duration-500 cursor-default'>
                A Space Where Your Stories — And Your Career — Can Grow.
              </span>
            </div>
            <div className='flex-1 h-1 bg-gradient-to-l from-transparent via-purple-400 to-purple-500'></div>
          </div>
        </div>
      </motion.div>

      {/* Authors Section */}
      <motion.div
        className='bg-blue-50'
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center'>
            {/* Left Section - Image */}
            <motion.div
              className='flex justify-center'
              initial={{ opacity: 0, x: -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <motion.img
                src={author}
                alt='Author Icon'
                className='w-full max-w-sm lg:max-w-md h-auto rounded-lg'
                whileHover={{
                  scale: 1.1,
                  rotate: 2,
                  boxShadow: "0 25px 50px rgba(0, 0, 0, 0.2)",
                  transition: { duration: 0.3 }
                }}
                whileTap={{ scale: 0.95 }}
              />
            </motion.div>

            {/* Right Section - Content */}
            <motion.div
              initial={{ opacity: 0, x: 100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <motion.h2
                className='text-3xl lg:text-4xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-300 cursor-default'
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                viewport={{ once: true }}
              >
                Authors
              </motion.h2>
              <motion.h3
                className='text-lg font-semibold text-gray-600 mb-8'
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                viewport={{ once: true }}
              >
                Create | Connect | Cash In
              </motion.h3>

              {/* Feature Points */}
              <div className='space-y-3 mb-8'>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>If writing is your calling, Skrivee is your stage.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Earn from every engagement and interaction on your published content.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Offer premium content to loyal readers through paid subscriptions.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Submit drafts to publications for real, paid writing opportunities.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Win cash and recognition through curated writing contests.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Grow your following by consistently publishing high-quality Skrivees.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Showcase your best work in a professional online portfolio.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Collaborate with brands that value meaningful, story-driven content creation.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Turn your passion into income. Turn your voice into value. One story at a time.</p>
                </div>
              </div>

              {/* Signup Button */}
              <motion.button
                className='bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-md transition duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 hover:-translate-y-1'
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.1, y: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/login')}
              >
                Sign Up
              </motion.button>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Divider Section 2 */}
      <div className='bg-blue-50 py-8'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-center'>
            <div className='flex-1 h-1 bg-gradient-to-r from-transparent via-blue-400 to-blue-500'></div>
            <div className='px-8'>
              <span className='text-lg font-medium text-gray-700 whitespace-nowrap'>
                Powering The Next Generation Of Digital Publishers.
              </span>
            </div>
            <div className='flex-1 h-1 bg-gradient-to-l from-transparent via-purple-400 to-purple-500'></div>
          </div>
        </div>
      </div>

      {/* Publications Section */}
      <div className='bg-blue-50'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center'>
            {/* Right Section - Content */}
            <div>
              <h2 className='text-3xl lg:text-4xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-300 cursor-default'>
                Publications
              </h2>
              <h3 className='text-lg font-semibold text-gray-600 mb-8'>
                Craft | Connect | Capitalize
              </h3>

              {/* Feature Points */}
              <div className='space-y-3 mb-8'>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Share blogs, poems, stories, and e-books to reach readers.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Attract high-quality submissions from new and emerging talent.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Define content needs clearly and invite relevant, aligned draft submissions.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Host branded contests to discover talent and drive engagement.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Monetize content through reader subscriptions and author-driven engagement metrics.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Collaborate with storytelling-driven brands on creative publishing campaigns.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>All your publishing tools in one place — from content to talent, submissions to contests, and earnings to payouts.</p>
                </div>
              </div>

              {/* Signup Button */}
              <button className='bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-md transition duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 hover:-translate-y-1'>
                Sign Up
              </button>
            </div>

            {/* Left Section - Image */}
            <div className='flex justify-center'>
              <img
                src={publication}
                alt='Publication Icon'
                className='w-full max-w-sm lg:max-w-md h-auto transform hover:scale-110 transition-transform duration-700 ease-in-out hover:-rotate-2 hover:shadow-2xl rounded-lg'
              />
            </div>
          </div>
        </div>
      </div>

      {/* Divider Section 3 */}
      <div className='bg-blue-50 py-8'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-center'>
            <div className='flex-1 h-1 bg-gradient-to-r from-transparent via-blue-400 to-blue-500'></div>
            <div className='px-8'>
              <span className='text-lg font-medium text-gray-700 whitespace-nowrap'>
                Turn Your Brand Message Into Meaningful Stories
              </span>
            </div>
            <div className='flex-1 h-1 bg-gradient-to-l from-transparent via-purple-400 to-purple-500'></div>
          </div>
        </div>
      </div>

      {/* Business Section */}
      <div className='bg-blue-50'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center'>
            {/* Left Section - Image */}
            <div className='flex justify-center'>
              <img
                src={business}
                alt='Business Icon'
                className='w-full max-w-sm lg:max-w-md h-auto transform hover:scale-110 transition-transform duration-700 ease-in-out hover:rotate-1 hover:shadow-2xl rounded-lg'
              />
            </div>

            {/* Right Section - Content */}
            <div>
              <h2 className='text-3xl lg:text-4xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-300 cursor-default'>
                Business
              </h2>
              <h3 className='text-lg font-semibold text-gray-600 mb-8'>
                Curate | Collaborate | Convert
              </h3>

              {/* Feature Points */}
              <div className='space-y-3 mb-8'>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Access a wide network of writers and digital publications instantly.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Post content briefs and receive targeted story submissions.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Commission original content — blogs, poems or story-telling campaigns.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Launch branded contests to drive engagement and community participation.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Collaborate with publications to get your brand stories featured and distributed.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Track campaign performance using real-time analytics and engagement dashboards.</p>
                </div>
                <div className='flex items-start hover:bg-blue-100 p-2 rounded-lg transition-all duration-300 hover:shadow-sm transform hover:scale-105'>
                  <span className='text-green-500 mr-3 mt-1.5 text-lg transform hover:scale-125 transition-transform duration-300'>✓</span>
                  <p className='text-gray-700 leading-relaxed'>Transform your brand messaging into compelling, story-driven content.</p>
                </div>
              </div>

              {/* Signup Button */}
              <button className='bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-md transition duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 hover:-translate-y-1'>
                Sign Up
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className='bg-white border-t border-gray-200'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
          <div className='flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8'>
            {/* Social Media Icons */}
            <div className='flex items-center space-x-4'>
              {/* Facebook */}
              <a href="#" className='text-blue-600 hover:text-blue-800 transition duration-300'>
                <svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/>
                </svg>
              </a>

              {/* Instagram */}
              <a href="#" className='text-pink-600 hover:text-pink-800 transition duration-300'>
                <svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z'/>
                </svg>
              </a>

              {/* Twitter */}
              <a href="#" className='text-blue-400 hover:text-blue-600 transition duration-300'>
                <svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'/>
                </svg>
              </a>

              {/* LinkedIn */}
              <a href="#" className='text-blue-700 hover:text-blue-900 transition duration-300'>
                <svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'/>
                </svg>
              </a>
            </div>

            {/* Footer Links */}
            <div className='flex flex-wrap justify-center items-center space-x-6 text-sm text-gray-600'>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>About Us</a>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>Privacy Policy</a>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>Terms and Conditions</a>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>Suggestions</a>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>Copyrights</a>
              <a href="#" className='hover:text-blue-500 transition duration-300 font-bold'>Contact Us</a>
            </div>
          </div>

          {/* Copyright */}
          <div className='mt-6 text-center'>
            <p className='text-sm text-gray-500 font-bold'>
              © copyrights 2025, Skrivee - All Rights Reserved
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Landing
