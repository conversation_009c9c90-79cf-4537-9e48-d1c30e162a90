import { useLocation, useNavigate } from 'react-router-dom';
import Dashboard from '../btns/Dashboard';
import Help from '../btns/Help';
import Logout from '../btns/Logout';
import Messages from '../btns/Messages';
import Notification from '../btns/Notification';
import Pitch from '../btns/Pitch';
import Ranking from '../btns/Ranking';
import Settings from '../btns/Settings';
import Skrivee from '../btns/Skrivee';
import Logo from '/logo/Logo.svg';

function Sidebar() {
    const location = useLocation();
    const navigate = useNavigate();

    const color = "#4A99F8";
    const height = 22;
    const width = 24;
    const isActive = (path) => location.pathname === path;

    const SidebarItem = ({ path, render }) => (
        <div
            onClick={() => navigate(path)}
            className={`cursor-pointer transition-all duration-200 rounded-l-md flex items-center hover:scale-105
        ${isActive(path) ? 'border-r-4 border-[#4A99F8]' : 'hover:border-r-4 hover:border-[#80c3d4]'}
      `}
        >
            {render()}
        </div>
    );
return (
  <div className="w-auto h-screen  flex flex-col">
    {/* TOP SECTION: Logo + Avatar */}
    <div>
      <img src={Logo} className="h-14 w-32 mb-4" onClick={() => navigate('/home')} />
      <SidebarItem path="/profile" render={() => (
        <div className="avatar flex-1 flex justify-center items-center">
          <div className="ring-primary ring-offset-base-100 w-14 h-14 rounded-full ring-2 ring-offset-2 overflow-hidden">
            <img
              src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
              className="w-full h-full object-cover rounded-full"
              alt="Profile"
            />
          </div>
        </div>
      )} />
    </div>

    {/* MIDDLE SECTION: Nav Items - evenly spaced */}
    <div className="flex flex-col justify-evenly flex-grow">
      <SidebarItem path="/skrivee" render={() => <Skrivee color={color} h={height} w={width} txt="Skrivee" />} />
      <SidebarItem path="/ranking" render={() => <Ranking color={color} h={height} w={width} txt="Ranking" />} />
      <SidebarItem path="/pitch" render={() => <Pitch color={color} h={height} w={width} txt="Pitch" />} />
      <SidebarItem path="/messages" render={() => <Messages color={color} h={height} w={width} txt="Messages" />} />
      <SidebarItem path="/notifications" render={() => <Notification color={color} h={height} w={width} txt="Notification" />} />
      <SidebarItem path="/dashboard" render={() => <Dashboard color={color} h={height} w={width} txt="Dashboard" />} />
      <SidebarItem path="/help" render={() => <Help color={color} h={height} w={width} txt="Help" />} />
      <SidebarItem path="/settings" render={() => <Settings color={color} h={height} w={width} txt="Settings" />} />
      <Logout color={color} h={height} w={width} txt="Logout" />
    </div>

  </div>
)

}


export default Sidebar