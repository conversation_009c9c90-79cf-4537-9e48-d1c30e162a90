const mysql = require('mysql2/promise')

// console.log("checking for the data : ",process.env.SQLHOST,process.env.SQLUSER,process.env.SQLPASSWORD,process.env.SQLDATABASE)

const pool = mysql.createPool({
    host:process.env.SQLHOST,
    user:process.env.SQLUSER,
    password:process.env.SQLPASSWORD,
    database:process.env.SQLDATABASE,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    connectTimeout: 30000
})

pool.getConnection().then(connection=>{
    console.log('Successfully connected to mysql database');
    connection.release()
}).catch(err=>{
    console.log('Error connecting to mysql : ',err)
    process.exit(1)
})

module.exports = pool;