import React from 'react'

const messages = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M14.9344 0C11.7014 0 9.18837 1.37554e-07 7.23209 0.230777C5.25349 0.46617 3.68651 0.953879 2.43628 2.05546C1.15256 3.1878 0.552558 4.66016 0.269302 6.51715C-6.2377e-08 8.28952 0 10.545 0 13.3574V13.6389C0 16.3806 6.2377e-08 18.2775 0.27907 19.6914C0.429767 20.453 0.666977 21.1146 1.03953 21.7269C1.40791 22.33 1.87674 22.8377 2.43628 23.33C3.31674 24.107 4.35488 24.5762 5.5814 24.8685V28.8471C5.58156 29.0493 5.62989 29.2479 5.72155 29.423C5.81321 29.598 5.94498 29.7435 6.10366 29.8448C6.26235 29.946 6.44238 29.9996 6.62573 30C6.80907 30.0004 6.98931 29.9477 7.14837 29.8472C7.96605 29.3318 8.69023 28.7702 9.35302 28.241L9.77721 27.901C10.2492 27.5114 10.7312 27.1368 11.2228 26.7778C12.4005 25.9347 13.5251 25.3855 15 25.3855H15.0656C18.2986 25.3855 20.8116 25.3855 22.7679 25.1547C24.7465 24.9193 26.3135 24.4316 27.5637 23.33C28.1219 22.8377 28.5921 22.33 28.9591 21.7269C29.333 21.1146 29.5702 20.453 29.7209 19.6914C30 18.2775 30 16.3806 30 13.6389V13.3574C30 10.545 30 8.28952 29.7307 6.51869C29.4474 4.66016 28.8474 3.1878 27.5637 2.05546C26.3135 0.95234 24.7465 0.46617 22.7679 0.232316C20.8116 4.58513e-08 18.2986 0 15.0656 0H14.9344ZM18.8847 8.6803C19.1096 8.85945 19.2609 9.12979 19.3051 9.43187C19.3493 9.73395 19.2829 10.043 19.1205 10.2911L15.2135 16.2544C15.0845 16.4507 14.917 16.6126 14.7235 16.7278C14.53 16.8431 14.3155 16.9088 14.0958 16.9202C13.8762 16.9316 13.657 16.8883 13.4546 16.7935C13.2522 16.6988 13.0717 16.555 12.9265 16.3729L11.0037 13.9482C10.9069 13.8354 10.8317 13.7022 10.7826 13.5565C10.7336 13.4109 10.7118 13.2559 10.7184 13.1007C10.725 12.9456 10.7599 12.7935 10.8212 12.6536C10.8824 12.5137 10.9686 12.3888 11.0746 12.2865C11.1806 12.1843 11.3043 12.1066 11.4382 12.0583C11.572 12.01 11.7134 11.992 11.8538 12.0053C11.9941 12.0187 12.1306 12.0632 12.255 12.1361C12.3794 12.209 12.4891 12.3088 12.5777 12.4297L13.9814 14.1959L17.4237 8.94031C17.5862 8.69224 17.8314 8.5255 18.1054 8.47674C18.3793 8.42798 18.6596 8.5012 18.8847 8.6803Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}

function Messages({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M14.9344 0C11.7014 0 9.18837 1.37554e-07 7.23209 0.230777C5.25349 0.46617 3.68651 0.953879 2.43628 2.05546C1.15256 3.1878 0.552558 4.66016 0.269302 6.51715C-6.2377e-08 8.28952 0 10.545 0 13.3574V13.6389C0 16.3806 6.2377e-08 18.2775 0.27907 19.6914C0.429767 20.453 0.666977 21.1146 1.03953 21.7269C1.40791 22.33 1.87674 22.8377 2.43628 23.33C3.31674 24.107 4.35488 24.5762 5.5814 24.8685V28.8471C5.58156 29.0493 5.62989 29.2479 5.72155 29.423C5.81321 29.598 5.94498 29.7435 6.10366 29.8448C6.26235 29.946 6.44238 29.9996 6.62573 30C6.80907 30.0004 6.98931 29.9477 7.14837 29.8472C7.96605 29.3318 8.69023 28.7702 9.35302 28.241L9.77721 27.901C10.2492 27.5114 10.7312 27.1368 11.2228 26.7778C12.4005 25.9347 13.5251 25.3855 15 25.3855H15.0656C18.2986 25.3855 20.8116 25.3855 22.7679 25.1547C24.7465 24.9193 26.3135 24.4316 27.5637 23.33C28.1219 22.8377 28.5921 22.33 28.9591 21.7269C29.333 21.1146 29.5702 20.453 29.7209 19.6914C30 18.2775 30 16.3806 30 13.6389V13.3574C30 10.545 30 8.28952 29.7307 6.51869C29.4474 4.66016 28.8474 3.1878 27.5637 2.05546C26.3135 0.95234 24.7465 0.46617 22.7679 0.232316C20.8116 4.58513e-08 18.2986 0 15.0656 0H14.9344ZM18.8847 8.6803C19.1096 8.85945 19.2609 9.12979 19.3051 9.43187C19.3493 9.73395 19.2829 10.043 19.1205 10.2911L15.2135 16.2544C15.0845 16.4507 14.917 16.6126 14.7235 16.7278C14.53 16.8431 14.3155 16.9088 14.0958 16.9202C13.8762 16.9316 13.657 16.8883 13.4546 16.7935C13.2522 16.6988 13.0717 16.555 12.9265 16.3729L11.0037 13.9482C10.9069 13.8354 10.8317 13.7022 10.7826 13.5565C10.7336 13.4109 10.7118 13.2559 10.7184 13.1007C10.725 12.9456 10.7599 12.7935 10.8212 12.6536C10.8824 12.5137 10.9686 12.3888 11.0746 12.2865C11.1806 12.1843 11.3043 12.1066 11.4382 12.0583C11.572 12.01 11.7134 11.992 11.8538 12.0053C11.9941 12.0187 12.1306 12.0632 12.255 12.1361C12.3794 12.209 12.4891 12.3088 12.5777 12.4297L13.9814 14.1959L17.4237 8.94031C17.5862 8.69224 17.8314 8.5255 18.1054 8.47674C18.3793 8.42798 18.6596 8.5012 18.8847 8.6803Z" fill={color} />
            </svg>
    </div>
    <div className="hidden md:flex">
        {messages(color,h,w,txt)}
    </div>
    </>
  )
}

export default Messages