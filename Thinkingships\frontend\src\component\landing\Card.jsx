import React from 'react';
import { useNavigate } from 'react-router-dom';

function Card({ cardTitle, cardControls, cardDesc, pitch, signUp, img: ImgComponent, direction = 'left' }) {
    const navigate = useNavigate();
    const isRight = direction === 'right';

    return (
        <div className={`card lg:card-side bg-base-100 shadow-sm flex flex-col lg:flex-row ${isRight ? 'lg:flex-row-reverse' : ''} my-8`}>
            {/* Image */}
            <figure className="flex justify-center items-center w-full lg:w-1/2 p-4">
                {typeof ImgComponent === 'string'
                    ? <img src={ImgComponent} alt={cardTitle} className="w-full h-auto max-h-[300px] object-contain" />
                    : <ImgComponent className="max-w-full max-h-64" />}
            </figure>

            {/* Content */}
            <div className="card-body flex flex-col justify-center w-full lg:w-1/2 p-6">
                {/* Title */}
                <h2 className="text-3xl font-bold text-center mb-2">{cardTitle}</h2>

                {/* Controls line */}
                <p className="text-lg text-center text-gray-600 font-medium mb-4">{cardControls}</p>

                {/* Description List */}
                <div className="space-y-4">
                    {cardDesc.map((item, index) => (
                        <div key={index}>
                            <h3 className="text-lg font-semibold text-gray-800">{item.title}</h3>
                            <p className="text-sm text-gray-600">{item.desc}</p>
                        </div>
                    ))}
                </div>

                {/* Pitch */}
                <p className="text-center text-base font-medium text-gray-700 mt-6">{pitch}</p>

                {/* Sign up Button */}
                <div className="mt-4 flex justify-center">
                    <button
                        className="btn btn-primary px-6"
                        onClick={() => navigate(`/signup/${signUp}`)}
                    >
                        Sign Up
                    </button>
                </div>
            </div>
        </div>
    );
}

export default Card;
