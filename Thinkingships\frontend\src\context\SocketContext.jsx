import { createContext,useContext,useEffect,useState } from "react";
import {useSelector} from 'react-redux'
import io from 'socket.io-client'

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;
const SERVER = import.meta.env.VITE_ENV_TYPE;

const SocketContext = createContext();

export const UseSocketContext = () => {
    return useContext(SocketContext);
}

export const SocketContextProvider = ({children}) =>{
    const [socket,setSocket] = useState(null);
    const [onlineUser,setOnlineUser] = useState([]);
    const {user} = useSelector((state)=>state.user);

    useEffect(()=>{
        if(user?.id){
            const socketInstance = io(BACKEND_URL,{
                query:{
                    userId: user.id
                },
                transports:["websocket"]
            })
            setSocket(socketInstance);
            console.log(socketInstance)
            socketInstance.on("getOnlineUsers",(users)=> setOnlineUser(users))
             return () => {
            socketInstance.disconnect();
            setSocket(null);
        }
        }
       
    },[user?.id])
    return(
        <SocketContext.Provider value = {{socket,onlineUser}}>
            {children}
        </SocketContext.Provider>
    )
}