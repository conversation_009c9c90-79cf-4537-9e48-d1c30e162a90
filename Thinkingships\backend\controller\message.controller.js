const catchAsyncError = require('../middleware/catchAsyncError')
const { generateUUID } = require('../utils/generateIDS')
const ErrorHandler = require('../errors/ErrorHandler')
const pool = require('../config/sqlConnector')
const {io} = require('./../app')
const {getReceiverSocket} = require("./../socket/socketHandler")
const {
    uploadToCloudinary,
    deleteFromCloudinary
} = require('../config/cloudinaryConfig')

exports.sendMessage = catchAsyncError(async (req, res, next) => {
  const userId = req.user.id;
  const { id: receiverId, image = '', messageType = 'text' } = req.params;
  const { text } = req.body;

  if (!text && !image) {
    return next(new ErrorHandler('Add some input to send', 400));
  }

  // Check if conversation already exists between userId and receiverId
  const [[existingConversation]] = await pool.query(
    `SELECT id FROM conversations 
     WHERE (user1_id = ? AND user2_id = ?) 
        OR (user1_id = ? AND user2_id = ?)`,
    [userId, receiverId, receiverId, userId]
  );

  const conversationId = existingConversation?.id || generateUUID();

  const queries = [];

  // If conversation doesn't exist, create it
  if (!existingConversation) {
    queries.push(
      pool.query(
        `INSERT INTO conversations (id, user1_id, user2_id) VALUES (?, ?, ?)`,
        [conversationId, userId, receiverId]
      )
    );
  }

  // Insert the message
  queries.push(
    pool.query(
      `INSERT INTO messages (conversation_id, sender_id, userText, image, messageType) VALUES (?, ?, ?, ?, ?)`,
      [conversationId, userId, text || null, image || null, messageType]
    )
  );

  const results = await Promise.all(queries);

  const messageInsertResult = results[results.length - 1][0];
  const messageId = messageInsertResult.insertId;

  const message = {
    id: messageId,
    conversation_id: conversationId,
    sender_id: userId,
    userText: text || null,
    image: image || null,
    messageType,
    sent_at: new Date(),
  };

  // Emit socket event to the receiver
  const receiverSocket = getReceiverSocket(receiverId);
  if (io && receiverSocket) {
    io.to(receiverSocket).emit("newMessage", message);
  }

  res.status(200).json({ success: true, message });
});



exports.getMessage = catchAsyncError(async(req,res,next)=>{
    const userId = req?.user?.id;
    const {id:receiverId} = req.params;

    const [[existingConversation]] = await pool.query(
    `SELECT id FROM conversations 
     WHERE (user1_id = ? AND user2_id = ?) 
        OR (user1_id = ? AND user2_id = ?)`,
    [userId, receiverId, receiverId, userId]
  );

  const conversationId = existingConversation?.id 

  let result = [];

  const [rows] = await pool.query(`Select * from messages where conversationId = ?`,[conversationId])
  res.status(200).json(success:true,messages:rows)

})