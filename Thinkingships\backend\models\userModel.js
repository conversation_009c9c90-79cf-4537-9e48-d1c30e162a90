
// CREATE TABLE IF NOT EXISTS User(
// id VARCHAR(36) PRIMARY KEY,
// userName VARCHAR(25) NOT NULL,
// fullName varchar(255),
// phone VARCHAR(20) UNIQUE,
// email varchar(255) unique not null,
// hshPassword varchar(255) NOT NULL,
// userRole ENUM('admin', 'publication','viewer') DEFAULT 'viewer',
// createdAt timestamp Default CURRENT_TIMESTAMP,
// updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
// ); 