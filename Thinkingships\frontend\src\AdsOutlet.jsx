import React, { useEffect } from "react";
import { Outlet } from "react-router-dom";

function AdsOutletComponent() {
  useEffect(()=>{
    console.log("ads mounting check")
    return ()=> console.log("ads unmounting")
  },[])

  // Sample ads data
  const ads = [
    {
      id: 1,
      title: "Best Writing Tools",
      description: "Discover amazing writing tools for authors",
      image: "https://picsum.photos/200/150?random=1",
      link: "#"
    },
    {
      id: 2,
      title: "Publishing Services",
      description: "Get your book published professionally",
      image: "https://picsum.photos/200/150?random=2",
      link: "#"
    },
    {
      id: 3,
      title: "Writing Course",
      description: "Learn creative writing from experts",
      image: "https://picsum.photos/200/150?random=3",
      link: "#"
    },
    {
      id: 4,
      title: "Book Marketing",
      description: "Promote your books effectively",
      image: "https://picsum.photos/200/150?random=4",
      link: "#"
    },
    {
      id: 5,
      title: "Author Community",
      description: "Join thousands of writers worldwide",
      image: "https://picsum.photos/200/150?random=5",
      link: "#"
    },
    {
      id: 6,
      title: "Writing Software",
      description: "Professional writing and editing tools",
      image: "https://picsum.photos/200/150?random=6",
      link: "#"
    },
    {
      id: 7,
      title: "Book Cover Design",
      description: "Beautiful covers for your books",
      image: "https://picsum.photos/200/150?random=7",
      link: "#"
    },
    {
      id: 8,
      title: "Grammar Checker",
      description: "Perfect your writing with AI",
      image: "https://picsum.photos/200/150?random=8",
      link: "#"
    }
  ];

  return (
    <div className="flex flex-1">
  <div
    className="hidden md:fixed md:flex md:flex-col right-0 top-0 w-[250px] overflow-y-auto bg-gray-50 border-l border-gray-200 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
    style={{ height: "calc(100% - 64.67px)",top: "64.67px"  }}
  >
    {/* Ads Header */}
    <div className="sticky top-0 bg-white border-b border-gray-200 p-3 z-10">
      <h3 className="text-sm font-semibold text-gray-700">Sponsored</h3>
    </div>

    {/* Scrollable Ads Content */}
    <div className="flex-1 p-3 space-y-4">
      {ads.map((ad) => (
        <div
          key={ad.id}
          className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
          onClick={() => window.open(ad.link, '_blank')}
        >
          <img
            src={ad.image}
            alt={ad.title}
            className="w-full h-24 object-cover"
            onError={(e) => {
              e.target.src = `https://via.placeholder.com/200x150/3B82F6/FFFFFF?text=Ad+${ad.id}`;
            }}
          />
          <div className="p-3">
            <h4 className="text-sm font-medium text-gray-800 mb-1 line-clamp-2">
              {ad.title}
            </h4>
            <p className="text-xs text-gray-600 line-clamp-2">
              {ad.description}
            </p>
            <button className="mt-2 text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors">
              Learn More
            </button>
          </div>
        </div>
      ))}

      {/* Add more space at bottom for better scrolling */}
      <div className="h-4"></div>
    </div>
  </div>
{/* flex-1 md:mt-10 mt-20 py-6 pl-6 pr-6 md:pr-0 min-h-screen md:mb-0 mb-20 max-w-screen overflow-x-hidden */}
  <div className="md:mr-[250px] md:pr-6 pr-0 flex-1 md:mt-8 w-full">
    <Outlet />
  </div>
</div>
  );
}
const AdsOutlet = React.memo(AdsOutletComponent);
export default AdsOutlet;
