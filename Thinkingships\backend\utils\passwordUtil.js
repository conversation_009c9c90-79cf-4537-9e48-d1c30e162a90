const bcrypt = require('bcrypt')

exports.hashPassword = async(password)=>{
    try{
        const salt = await bcrypt.genSalt(10);
        return await bcrypt.hash(password,salt);
    }catch(error){
        console.log("error while generating password hash - ",error);
        return null;
    }
}

exports.comparePassword = async(hshPass,enteredPass)=>{
    try{
        return await bcrypt.compare(enteredPass,hshPass )
    }catch(error){
        console.log("error comparing the password - ",error)
        return false;
    }
    
}