const catchAsyncError = require("../middleware/catchAsyncError");
const { generateUUID } = require('../utils/generateIDS');
const ErrorHandler = require("../errors/ErrorHandler");
const pool = require("../config/sqlConnector");
const {  uploadToCloudinary, deleteFromCloudinary } = require('../config/cloudinaryConfig')

// return type boolean
const findAge = (userAge) => {
    try {
        const today = new Date();
        const birthDate = new Date(userAge);
        let age = today.getFullYear() - birthDate.getFullYear();

        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        return age >= 18;
    } catch (error) {
        console.log(`error while finding the age `, error)
        return false;
    }
}


exports.saveOrPublishSkrivee = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const {
        id, // may or may not exist
        title,
        skriveeTxt,
        abstract,
        genre,
        tags,
        deticatedTo,
        ReadingLevel,
        languages,
        adultContent = false,
        subscription = false,
        isPublished = false
    } = req.body;
    const story_cover = req.file?.base64 || null;
    if (!title || !skriveeTxt || !abstract) {
        return res.status(400).json({ success: false, message: "Missing required fields" });
    }

    const publishedText = isPublished ? skriveeTxt : null;
    const savedSkriveeTxt = skriveeTxt;
    let story_url;

    // //  inputs url, foldername , type = image/video default image
    if (story_cover) {
        const response = await uploadToCloudinary(story_cover, "skrivees");
        if (!response) return next(new ErrorHandler("Something went wrong with uploading", 400));
        story_url = response;
    }

    const data = [
        title || null,
        savedSkriveeTxt || null, // savedSkriveeTxt
        publishedText,      // publishedSkriveeTxt
        story_url || null,
        abstract || null,
        genre || null,
        tags || null,
        deticatedTo || null,
        ReadingLevel || null,
        languages || null,
        adultContent,
        subscription,
        isPublished
    ];

    const skriveeId = id || generateUUID();

    if (id) {
        const existingUser = await pool.query('select story_cover from skrivee where id = ?', [skriveeId]);
        const existingImg = existingUser[0][0].story_cover ? existingUser[0][0].story_cover : null;
        if (existingImg) {
            const res = await deleteFromCloudinary(existingImg)
            if (!res) return next(new ErrorHandler("Something went wrong while uploading the image", 400))
        }
        await pool.query(
            `UPDATE skrivee SET
        title = ?, savedSkriveeTxt = ?, publishedSkriveeTxt = ?, story_cover = ?, abstract = ?, 
        genre = ?, tags = ?, deticatedTo = ?, ReadingLevel = ?, languages = ?, adultContent = ?, 
        subscription = ?, isPublished = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE id = ? AND userId = ?`,
            [...data, skriveeId, userId]
        );
    } else {
        // Create new entry
        await pool.query(
            `INSERT INTO skrivee (
        id, userId, title, savedSkriveeTxt, publishedSkriveeTxt, story_cover, abstract, genre, tags,
        deticatedTo, ReadingLevel, languages, adultContent, subscription, isPublished
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [skriveeId, userId, ...data]
        );
    }

    const [uploadedSkrivee] = await pool.query("SELECT * FROM skrivee WHERE id = ?", [skriveeId]);

    return res.status(200).json({
        success: true,
        skrivee: uploadedSkrivee[0]
    });
});

exports.updateSkrivee = catchAsyncError(async (req, res, next) => {
    const {
        skriveeTxt,
        isPublished = false,
        subscription = false,
        adultContent = false
    } = req.body;
    const userId = req.user.id;
    const { skriveId } = req.params;

    const [rows] = await pool.query(`select * from skrivee where id = ? `, [skriveId]);
    if (rows.length === 0) return next(new ErrorHandler("Skrivee not found", 400));
    if (rows[0].userId !== userId) return next(new ErrorHandler("Not authorized to delete this resource", 403));

    let query;
    let publishedSkriveeTxt = skriveeTxt;
    let data = [
        skriveeTxt,
        subscription,
        adultContent
    ]
    if (isPublished) {
        query = `UPDATE skrivee SET
        publishedSkriveeTxt = ?, savedSkriveeTxt = ?, subscription = ?, updatedAt = CURRENT_TIMESTAMP, adultContent =? 
        WHERE id = ? AND userId = ?`
        data = [publishedSkriveeTxt, ...data];
    }
    else {
        query = `UPDATE skrivee SET
        savedSkriveeTxt = ?, subscription = ?, updatedAt = CURRENT_TIMESTAMP, adultContent =? 
        WHERE id = ? AND userId = ?`
    }
    await pool.query(query, [...data]);
    const [response] = await pool.query(`select * from skrivee where id = ? `, [skriveId]);
    const result = response[0];
    return res.status(200).json({ skrivee: result })
})


exports.deleteSkrivee = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const { skriveId } = req.params;

    const [rows] = await pool.query(`select * from skrivee where id = ? `, [skriveId]);
    if (rows.length === 0) return next(new ErrorHandler("Skrivee not found", 400));
    if (rows[0].userId !== userId) return next(new ErrorHandler("Not authorized to delete this resource", 403));

    await pool.query("Delete from skrivee where id = ?", [skriveId]);

    return res.status(200).json({
        success: true,
        deletedId: skriveId
    })
})

exports.getSingleSkrivee = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const { id: skriveeId } = req.params;
    const userRole = req.user.userRole;
    // we'll be needing more checks in the near future
    const [dob, rows] = await Promise.all([pool.query("select dob from author where userId = ? ", [userId]), pool.query(`select * from skrivee where id = ? `, [skriveeId])]);
    if (rows.length == 0) return next(new ErrorHandler("Skrivee Not found", 400));
    const skrivee = rows[0];
    if (skrivee.adultContent && userRole === 'author'  && !findAge(dob[0].dob)) {
        //  user is not old enough
        return res.status(200).json({ success: true, ageIssue: true, message: "user must be 18+ to view this content", skrivee: {} });
    }
    return res.status(200).json({ success: true, ageIssue: false, message: "content$$", skrivee })
})
