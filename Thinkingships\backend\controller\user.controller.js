const catchAsyncError = require("../middleware/catchAsyncError");
const { hashPassword, comparePassword } = require('../utils/passwordUtil')
const { generateUUID } = require('../utils/generateIDS');
const ErrorHandler = require("../errors/ErrorHandler");
const pool = require("../config/sqlConnector");
const emailConstants = require('../constants/emailConstants');
const emailService = require("../services/emailService");
const crypto = require('crypto');
const { sendToken } = require("../utils/TokenUtil");
const mysql = require('mysql2')
// TEN MINS 
const EXPIRE_TIME = 10 * 60 * 1000;
// Getting Link for email
// @return type : string
const generateLinkForVerification = async (userId) => {
    try {
        const rawToken = crypto.randomBytes(32).toString("hex"); // gonna send this to the user
        const hashedToken = crypto.createHash("sha-256").update(rawToken).digest("hex");
        const id = generateUUID();
        const expires_at = new Date(Date.now() + EXPIRE_TIME)
        await pool.query(
            `INSERT INTO email_verification_tokens 
            (id, userId, token, expires_at)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            token = VALUES(token),
            expires_at = VALUES(expires_at),
            created_at = NOW()`,
            [id, userId, hashedToken, expires_at]
        );
        const link = `${process.env.FRONTEND_URL}/verify-email?token=${rawToken}`;
        return {
            rawToken,
            hashedToken,
            link: link
        };
    } catch (error) {
        console.error("Error generating verification link:", error?.message || error);
        return null;
    }
}

const verifyToken = async (token) => {
    try {
        const hashedToken = crypto.createHash("sha256").update(token).digest("hex");
        const query = `
            SELECT userId 
            FROM email_verification_tokens 
            WHERE token = ? 
            AND expires_at > NOW()
            LIMIT 1
            `;
        const [rows] = await pool.query(query, [hashedToken]);
        return rows.length ? rows[0].userId : null;
    } catch (error) {
        console.log("error while verifying the token - ", error);
        return null;
    }
}

exports.createUser = catchAsyncError(async (req, res, next) => {
    const { userName, email, password, confirmPassword = "" } = req.body;
    const { userRole: userRole } = req.params;

    // Invalid input handling
    if (!email || !password || !confirmPassword) return next(new ErrorHandler("Please enter all the fields [email,password,confirmPassword]", 400));
    if (password != confirmPassword) return next(new ErrorHandler("password does not match", 400))
    if (userRole !== 'publication' && userRole !== 'business' && userRole !== 'author') return next(new ErrorHandler("Invalid UserRole", 403));

    const [rows] = await pool.query(
        'SELECT email, userName FROM users WHERE email = ? OR userName = ?',
        [email, userName]
    );

    // Existing user handling
    const firstRow = rows[0];
    if (firstRow?.email === email) return next(new ErrorHandler("Email already exists"));
    if (firstRow?.userName === userName) return next(new ErrorHandler("Username already exists"));

    // Generating user and sending email
    const id = generateUUID();
    const hashedPassword = await hashPassword(password);

    const [result] = await pool.query(
        'INSERT INTO users (id,email,userName,hshPassword,userRole) VALUES (?,?,?,?,?)',
        [id, email, userName, hashedPassword, userRole]
    );

    const [insertedUser] = await pool.query(
        `SELECT id, email, userName, userRole, isVerified, isProfileComplete, createdAt, updatedAt 
   FROM users WHERE id = ?`,
        [id]
    );
    const user = insertedUser[0];

    const { link } = await generateLinkForVerification(id);
    if (!link) return next(new ErrorHandler("Could not generate verification link", 500));

    const html = emailConstants['htmlForSignUp'].html(userName, link);
    const subject = emailConstants['htmlForSignUp']['subject'];
    
    // counting the total number of users in a particular rank
    const [{ count }] = await db.query('SELECT COUNT(*) as count FROM ranks WHERE rankType = ?', [userRole]);
    const newRank= 0;
    await pool.query("INSERT INTO ranks (userId,rankType,score) VALUES (?,?,?)",[id,userRole,newRank]);

    emailService({
        receiverId: email,
        subject,
        html
    });

    sendToken(res, user, 200);
})


exports.verifyEmail = catchAsyncError(async (req, res, next) => {
    const { token } = req.params;
    if (!token) return next(new ErrorHandler("Token missing", 400));

    const userId = await verifyToken(token);
    if (!userId) return next(new ErrorHandler("Invalid or expired token", 400));

    await Promise.all([
        pool.query("UPDATE users SET isVerified = TRUE WHERE id = ?", [userId]),
        pool.query("DELETE FROM email_verification_tokens WHERE userId = ?", [userId])
    ])
    
    const html = emailConstants['htmlForWelcome']['html'];
        const subject = emailConstants['htmlForWelcome']['subject']
        const receiverId = rows[0]?.email;
        const includeFooter = false
        emailService({ receiverId, subject, html, includeFooter })

    return res.status(200).json({
        success: true,
        message: "Email verified! You can now log in."
    });
});

exports.resendToken = catchAsyncError(async(req,res,next)=>{
    const id = req.user.id;
    const userName = req.user.userName;
    const email = req.user.email;
    const { link } = await generateLinkForVerification(id);
    if (!link) return next(new ErrorHandler("Could not generate verification link", 500));

    const html = emailConstants['htmlForSignUp'].html(userName, link);
    const subject = emailConstants['htmlForSignUp']['subject'];

    // Sending email
    // email service takes these inputs : receiverId,subject,html,includeFooter
    emailService({
        receiverId: email,
        subject,
        html
    });

    return res.status(200).json({success:true,link})
    
})

exports.authorizeUSer = catchAsyncError(async (req, res, next) => {
    const id = req.params;
    const [rows] = pool.query("SELECT email,userName, isVerified from users WHERE id = ? ", [id]);
    if (rows[0].isVerified) return res.status(200).json({ message: "You are already verified Please login to continue" });
    else {
        const [rows] = pool.query("ALTER TABLE users UPDATE isVerfied=true WHERE id = ? ", [id]);
        const html = emailConstants['htmlForWelcome']['html'];
        const subject = emailConstants['htmlForWelcome']['subject']
        const receiverId = rows[0]?.email;
        const includeFooter = false
        emailService({ receiverId, subject, html, includeFooter })
        return res.status(200).json({ message: "Your account is successfully verified now, Please login to proceed" });
    }
})

// this will show the user who logged in
exports.fetchMe = catchAsyncError(async (req, res, next) => {
    const user = req.user;
    return res.status(200).json({ user })
})

exports.logout = catchAsyncError(async(req,res,next)=>{
    res.cookie('token','',{
        httpOnly: process.env.ENV_TYPE !== 'dev',
        secure: process.env.ENV_TYPE !== 'dev',
        sameSite: process.env.ENV_TYPE === 'dev' ? 'lax' : 'none',
        expires: new Date(0),
    })
    return res.status(200).json({success:true,message:"successfully logged out"})
})

exports.loginUser = catchAsyncError(async (req, res, next) => {
    // getting email and password from the frontend
    const { userName = '', email = '', password } = req.body;
    // handling missing values
    if ((!email && !userName) || !password) return next(new ErrorHandler("Please enter email and password", 400));
    // finding the user
    const [rows] = await pool.query('SELECT id, email, userName, userRole, isVerified, isProfileComplete, hshPassword ,createdAt, updatedAt  FROM users WHERE email = ? OR userName = ?', [email, userName]);
    // email not found 
    if (!rows[0]) return next(new ErrorHandler("Invalid Email or Password", 400))

    // getting hashedPassword  
    const { hshPassword, ...user } = rows[0];
    const passwordMatched = await comparePassword(hshPassword, password);
    if (!passwordMatched) return next(new ErrorHandler("Invalid Email / User-name or Password", 400))
    sendToken(res, user, 200)
})

exports.fetchProfile = catchAsyncError(async(req,res,next)=>{
    const userId = req.user.id;
    const role = req.user.userRole
    const [userProfile] = await pool.query(`SELECT u.* , r.* FROM user_profiles as u left join ${role} as r on u.userId = r.userId WHERE u.userId = ? `,[userId]);
    let result = {}
    if(userProfile.length > 0)
        result = userProfile[0];
    return res.status(200).json({success:true,userProfile:result})
})

exports.updateProfile = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const userRole = req.user.userRole;

    if (!['author', 'publication', 'business'].includes(userRole)) {
        return next(new ErrorHandler("Invalid user role", 403));
    }

    const {
        userName,
        fullName,
        profilePic,
        phone,
        bio,
        location,
        userLanguage,
        social_web_url,
        social_insta_url,
        social_fb_url,
        social_linkedin_url,
        social_twitter_url,
        phoneHidden,
        emailHidden,
        // Author-specific
        passion,
        gender,
        dob,
        occupation,
        goodReads,
        // Publication-specific
        missionStatement,
        dateOfEstablishment,
        isbn_number,
        rni_number,
        gstin_number,
        gstin_hidden,
        rni_number_hidden,
        isbn_number_hidden,
        // Business-specific
        category,
        tagline,
        cin_number,
        pan_number,
        cin_hidden,
        pan_hidden,
    } = req.body;

    // --- 1. Handle user_profiles table ---
    const [profileRows] = await pool.query("SELECT * FROM user_profiles WHERE userId = ?", [userId]);

    const userProfileQueryData = [
        fullName || null,
        profilePic || null,
        phone || null,
        bio || null,
        location || null,
        userLanguage || null,
        social_web_url || null,
        social_insta_url || null,
        social_fb_url || null,
        social_linkedin_url || null,
        social_twitter_url || null,
        phoneHidden ?? true,
        emailHidden ?? true,
        userId,
    ];

    if (profileRows.length === 0) {
        // Insert new
        let id = generateUUID();
        await pool.query(
            `INSERT INTO user_profiles 
            (id,fullName, profilePic, phone, bio, location, userLanguage, social_web_url, social_insta_url, social_fb_url, social_linkedin_url, social_twitter_url, phoneHidden, emailHidden, userId) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [id, ...userProfileQueryData]
        );
    } else {
        // Update existing
        await pool.query(
            `UPDATE user_profiles SET 
            fullName=?, profilePic=?, phone=?, bio=?, location=?, userLanguage=?, social_web_url=?, social_insta_url=?, social_fb_url=?, social_linkedin_url=?, social_twitter_url=?, phoneHidden=?, emailHidden=? 
            WHERE userId = ?`,
            userProfileQueryData
        );
    }

    // --- 2. Handle role-specific table ---
    let roleQuery = '';
    let roleValues = [];
    let id = generateUUID();
    if (userRole === 'author') {
        const [rows] = await pool.query("SELECT * FROM author WHERE userId = ?", [userId]);
        roleValues = [passion || null, gender || null, dob || null, occupation || null, goodReads || null, userId];

        if (rows.length === 0) {
            roleQuery = `INSERT INTO author (id, passion, gender, dob, occupation, goodReads, userId) VALUES (?, ?, ?, ?, ?, ?, ?)`;
            roleValues = [id,...roleValues];
        } else {
            roleQuery = `UPDATE author SET passion=?, gender=?, dob=?, occupation=?, goodReads=? WHERE userId = ?`;
        }
    }

    if (userRole === 'publication') {
        const [rows] = await pool.query("SELECT * FROM publication WHERE userId = ?", [userId]);
        roleValues = [
            missionStatement || null, dateOfEstablishment || null, isbn_number || null, rni_number || null, gstin_number || null,
            gstin_hidden ?? true, rni_number_hidden ?? true, isbn_number_hidden ?? true, userId
        ];

        if (rows.length === 0) {
            roleQuery = `INSERT INTO publication (id, missionStatement, dateOfEstablishment, isbn_number, rni_number, gstin_number, gstin_hidden, rni_number_hidden, isbn_number_hidden, userId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            roleValues = [id,...roleValues];
        } else {
            roleQuery = `UPDATE publication SET missionStatement=?, dateOfEstablishment=?, isbn_number=?, rni_number=?, gstin_number=?, gstin_hidden=?, rni_number_hidden=?, isbn_number_hidden=? WHERE userId = ?`;
        }
    }

    if (userRole === 'business') {
        const [rows] = await pool.query("SELECT * FROM business WHERE userId = ?", [userId]);
        roleValues = [
            category || null, tagline || null, gstin_number || null, cin_number || null, pan_number || null,
            gstin_hidden ?? true, cin_hidden ?? true, pan_hidden ?? true, userId
        ];

        if (rows.length === 0) {
            roleQuery = `INSERT INTO business (id, category, tagline, gstin_number, cin_number, pan_number, gstin_hidden, cin_hidden, pan_hidden, userId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            roleValues = [id,...roleValues];
        } else {
            roleQuery = `UPDATE business SET category=?, tagline=?, gstin_number=?, cin_number=?, pan_number=?, gstin_hidden=?, cin_hidden=?, pan_hidden=? WHERE userId = ?`;
        }
    }

    if (roleQuery) {
        await pool.query(roleQuery, roleValues);
    }
    const [user] = await pool.query(`Select userName from  users  where userName= ? AND id != ?`, [userName,userId]);
    if(user.length > 0) return next(new ErrorHandler("UserName already exists choose something else",400));

    await pool.query(`UPDATE users SET isProfileComplete = true, userName = ? WHERE id = ?`, [userName, userId]);
    const [userProfile] = await pool.query(`SELECT u.* , r.* FROM user_profiles as u left join ${userRole} as r on u.userId = r.userId WHERE u.userId = ? `,[userId]);

    return res.status(200).json({
        success: true,
        message: "Profile saved successfully",
        userProfile:userProfile[0]
    });
});

exports.showProfile = catchAsyncError(async (req, res, next) => {
  const userId = req.user.id;
  const { userId: reqId } = req.params;
  let user;

  if (userId !== reqId) {
    const [rows] = await pool.query(`
      SELECT id, email, userName, userRole, isVerified, isProfileComplete, createdAt, updatedAt
      FROM users
      WHERE isDeleted = FALSE AND id = ?`, [reqId]);
    
    if (!rows.length) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    user = rows[0];
  } else {
    user = req.user;
  }

  const { userRole } = user;
    // Run parallel queries using Promise.all
  const [followersAndFollowingResult, profileResult] = await Promise.all([
    pool.query(`
      SELECT 
        (SELECT COUNT(*) FROM fansAndFaves WHERE faves_id = ?) AS followerCount,
        (SELECT COUNT(*) FROM fansAndFaves WHERE fans_id = ?) AS followingCount
    `, [user.id, user.id]),

    pool.query(`
      SELECT p.*, r.*, rt.* 
      FROM user_profiles AS p 
      LEFT JOIN ${userRole} AS r ON p.userId = r.userId 
      LEFT JOIN ranks AS rt ON p.userId = rt.userId 
      WHERE p.userId = ?
    `, [user.id])
  ]);
  console.log(followersAndFollowingResult,"\n",profileResult)
  const followersAndFollowing = followersAndFollowingResult[0][0] || {}; // because query returns [rows, fields]
  const profile = profileResult[0][0] || {};

  return res.status(200).json({
    success: true,
    followersAndFollowing,
    profile
    });
});
