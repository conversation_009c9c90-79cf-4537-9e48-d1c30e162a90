import React from 'react'
const help = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M14.925 24C15.45 24 15.894 23.8185 16.257 23.4555C16.62 23.0925 16.801 22.649 16.8 22.125C16.799 21.601 16.618 21.157 16.257 20.793C15.896 20.429 15.452 20.248 14.925 20.25C14.398 20.252 13.9545 20.4335 13.5945 20.7945C13.2345 21.1555 13.053 21.599 13.05 22.125C13.047 22.651 13.2285 23.095 13.5945 23.457C13.9605 23.819 14.404 24 14.925 24ZM13.575 18.225H16.35C16.35 17.4 16.444 16.75 16.632 16.275C16.82 15.8 17.351 15.15 18.225 14.325C18.875 13.675 19.3875 13.056 19.7625 12.468C20.1375 11.88 20.325 11.174 20.325 10.35C20.325 8.95 19.8125 7.875 18.7875 7.125C17.7625 6.375 16.55 6 15.15 6C13.725 6 12.569 6.375 11.682 7.125C10.795 7.875 10.176 8.775 9.825 9.825L12.3 10.8C12.425 10.35 12.7065 9.8625 13.1445 9.3375C13.5825 8.8125 14.251 8.55 15.15 8.55C15.95 8.55 16.55 8.769 16.95 9.207C17.35 9.64499 17.55 10.126 17.55 10.65C17.55 11.15 17.4 11.619 17.1 12.057C16.8 12.495 16.425 12.901 15.975 13.275C14.875 14.25 14.2 14.9875 13.95 15.4875C13.7 15.9875 13.575 16.9 13.575 18.225ZM15 30C12.925 30 10.975 29.6065 9.15 28.8195C7.325 28.0325 5.7375 26.9635 4.3875 25.6125C3.0375 24.2615 1.969 22.674 1.182 20.85C0.395002 19.026 0.0010019 17.076 1.89873e-06 15C-0.000998101 12.924 0.393002 10.974 1.182 9.15C1.971 7.326 3.0395 5.7385 4.3875 4.3875C5.7355 3.0365 7.323 1.96801 9.15 1.18201C10.977 0.396007 12.927 0.00200757 15 7.57574e-06C17.073 -0.00199242 19.023 0.392007 20.85 1.18201C22.677 1.97201 24.2645 3.0405 25.6125 4.3875C26.9605 5.7345 28.0295 7.322 28.8195 9.15C29.6095 10.978 30.003 12.928 30 15C29.997 17.072 29.603 19.022 28.818 20.85C28.033 22.678 26.9645 24.2655 25.6125 25.6125C24.2605 26.9595 22.673 28.0285 20.85 28.8195C19.027 29.6105 17.077 30.004 15 30Z"
                    fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}
function Help({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M14.925 24C15.45 24 15.894 23.8185 16.257 23.4555C16.62 23.0925 16.801 22.649 16.8 22.125C16.799 21.601 16.618 21.157 16.257 20.793C15.896 20.429 15.452 20.248 14.925 20.25C14.398 20.252 13.9545 20.4335 13.5945 20.7945C13.2345 21.1555 13.053 21.599 13.05 22.125C13.047 22.651 13.2285 23.095 13.5945 23.457C13.9605 23.819 14.404 24 14.925 24ZM13.575 18.225H16.35C16.35 17.4 16.444 16.75 16.632 16.275C16.82 15.8 17.351 15.15 18.225 14.325C18.875 13.675 19.3875 13.056 19.7625 12.468C20.1375 11.88 20.325 11.174 20.325 10.35C20.325 8.95 19.8125 7.875 18.7875 7.125C17.7625 6.375 16.55 6 15.15 6C13.725 6 12.569 6.375 11.682 7.125C10.795 7.875 10.176 8.775 9.825 9.825L12.3 10.8C12.425 10.35 12.7065 9.8625 13.1445 9.3375C13.5825 8.8125 14.251 8.55 15.15 8.55C15.95 8.55 16.55 8.769 16.95 9.207C17.35 9.64499 17.55 10.126 17.55 10.65C17.55 11.15 17.4 11.619 17.1 12.057C16.8 12.495 16.425 12.901 15.975 13.275C14.875 14.25 14.2 14.9875 13.95 15.4875C13.7 15.9875 13.575 16.9 13.575 18.225ZM15 30C12.925 30 10.975 29.6065 9.15 28.8195C7.325 28.0325 5.7375 26.9635 4.3875 25.6125C3.0375 24.2615 1.969 22.674 1.182 20.85C0.395002 19.026 0.0010019 17.076 1.89873e-06 15C-0.000998101 12.924 0.393002 10.974 1.182 9.15C1.971 7.326 3.0395 5.7385 4.3875 4.3875C5.7355 3.0365 7.323 1.96801 9.15 1.18201C10.977 0.396007 12.927 0.00200757 15 7.57574e-06C17.073 -0.00199242 19.023 0.392007 20.85 1.18201C22.677 1.97201 24.2645 3.0405 25.6125 4.3875C26.9605 5.7345 28.0295 7.322 28.8195 9.15C29.6095 10.978 30.003 12.928 30 15C29.997 17.072 29.603 19.022 28.818 20.85C28.033 22.678 26.9645 24.2655 25.6125 25.6125C24.2605 26.9595 22.673 28.0285 20.85 28.8195C19.027 29.6105 17.077 30.004 15 30Z"
                    fill={color} />
            </svg>
    </div>
    <div className="hidden md:flex">
        {help(color,h,w,txt)}
    </div>
    </>
  )
}

export default Help