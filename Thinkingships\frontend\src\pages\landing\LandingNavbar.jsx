import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import logo from '/logo/Logo.svg'

function LandingNavbar() {
    const navigate = useNavigate();
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    return (
        <nav className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-full mx-auto px-6">
                <div className="flex items-center justify-between h-16">
                    {/* Left side - Logo */}
                    <div className="flex items-center">
                        <img
                            src={logo}
                            alt="Skrivee Logo"
                            className="h-12 w-auto font-bold"
                        />
                    </div>

                    {/* Center - Navigation Links */}
                    <div className="hidden md:flex items-center space-x-8">
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 text-base font-bold transition duration-300 transform hover:scale-110 hover:-translate-y-1"
                        >
                            Romance
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 text-base font-bold transition duration-300 transform hover:scale-110 hover:-translate-y-1"
                        >
                            Fantasy
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 text-base font-bold transition duration-300 transform hover:scale-110 hover:-translate-y-1"
                        >
                            Mystery
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 text-base font-bold transition duration-300 transform hover:scale-110 hover:-translate-y-1"
                        >
                            Browse All
                        </a>
                    </div>

                    {/* Right side - SKRIVEE with pen icon and Login button */}
                    <div className="hidden md:flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                            <svg className="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                            <span className="text-gray-700 font-bold text-base">
                                SKRIVEE
                            </span>
                        </div>
                        <button
                            onClick={() => navigate("/login")}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md text-sm font-medium transition duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
                        >
                            Log In
                        </button>
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            onClick={() => setIsMenuOpen(!isMenuOpen)}
                            className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                        >
                            <svg
                                className="h-6 w-6"
                                stroke="currentColor"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                {isMenuOpen ? (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                ) : (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                                )}
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            {isMenuOpen && (
                <div className="md:hidden">
                    <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 block px-3 py-2 text-base font-bold"
                        >
                            Romance
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 block px-3 py-2 text-base font-bold"
                        >
                            Fantasy
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 block px-3 py-2 text-base font-bold"
                        >
                            Mystery
                        </a>
                        <a
                            href="#"
                            className="text-gray-700 hover:text-blue-500 block px-3 py-2 text-base font-bold"
                        >
                            Browse All
                        </a>
                        <div className="border-t border-gray-200 pt-4">
                            <div className="flex items-center justify-between px-3">
                                <div className="flex items-center space-x-1">
                                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                    <span className="text-gray-700 font-bold text-base">
                                        SKRIVEE
                                    </span>
                                </div>
                                <button
                                    onClick={() => navigate("/login")}
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-1.5 rounded text-sm font-medium transition duration-300"
                                >
                                    Log In
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </nav>
    )
}

export default LandingNavbar