import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    userLoading: null,
    isAuthenticated: false,
    user: null,
    userError: null,
};

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        loginRequest(state) {
            state.userLoading = true;
            state.isAuthenticated = false;
            state.userError = null;
        },
        loginSuccess(state, action) {
            state.userLoading = false;
            state.isAuthenticated = true;
            state.user = action.payload;
        },
        loginFail(state, action) {
            state.userLoading = false;
            state.isAuthenticated = false;
            state.user = null;
            state.userError = action.payload;
        },

        registerRequest(state) {
            state.userLoading = true;
            state.isAuthenticated = false;
            state.userError = null;
        },
        registerSuccess(state, action) {
            state.userLoading = false;
            state.isAuthenticated = true;
            state.user = action.payload;
        },
        registerFail(state, action) {
            state.userLoading = false;
            state.isAuthenticated = false;
            state.user = null;
            state.userError = action.payload;
        },

        logoutRequest(state) {
            state.userLoading = true;
            state.userError = null;
        },
        logoutSuccess(state) {
            state.userLoading = false;
            state.isAuthenticated = false;
            state.user = null;
        },
        logoutFail(state, action) {
            state.userLoading = false;
            state.userError = action.payload;
        },

        fetchMeRequest(state){
            state.userLoading = true;
            state.isAuthenticated = false;
            state.userError = null;
        },
        fetchMeSuccess(state,action){
            state.userLoading = false;
            state.isAuthenticated = true;
            state.user = action.payload;
        },
        fetchMeFail(state,action){
            state.userLoading = false;
            state.isAuthenticated = false;
            state.user = null;
            state.userError = action.payload;
        },


        clearErrors(state) {
            state.userError = null;
        },
    },
});

export const {
    loginRequest,
    loginSuccess,
    loginFail,
    registerRequest,
    registerSuccess,
    registerFail,
    logoutRequest,
    logoutSuccess,
    logoutFail,
    clearErrors,
    fetchMeRequest,
    fetchMeSuccess,
    fetchMeFail
} = userSlice.actions;

export default userSlice.reducer;
