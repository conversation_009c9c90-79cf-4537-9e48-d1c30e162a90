import React from 'react';
import { FaInstagram, FaFacebook, FaLinkedin, FaTwitter, FaGlobe, FaGoodreads } from 'react-icons/fa';

const inputClass =
  "w-full border px-3 py-2 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#4A99F8] bg-white";

function SocialLinks({ formData, onChange }) {
  const handleInput = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value }); // updates a single field in formData
  };

  const fields = [
    { label: "Website", name: "social_web_url", icon: <FaGlobe /> },
    { label: "Instagram", name: "social_insta_url", icon: <FaInstagram /> },
    { label: "Facebook", name: "social_fb_url", icon: <FaFacebook /> },
    { label: "LinkedIn", name: "social_linkedin_url", icon: <FaLinkedin /> },
    { label: "Twitter", name: "social_twitter_url", icon: <FaTwitter /> },
    { label: "Goodreads", name: "social_goodReads_url", icon: <FaGoodreads /> },
  ];

  return (
    <div className="space-y-4 mt-6">
      <h3 className="font-semibold text-lg">Social Media Links</h3>
      {fields.map((field) => (
        <div key={field.name} className="flex items-center gap-4">
          <div className="w-10 h-10 flex items-center justify-center rounded-full bg-white shadow-sm text-[#D99FE9] text-xl">
            {field.icon}
          </div>
          <input
            type="url"
            name={field.name}
            value={formData[field.name] || ''}
            onChange={handleInput}
            placeholder={`Enter your ${field.label} link`}
            className={inputClass}
          />
        </div>
      ))}
    </div>
  );
}


export default SocialLinks;