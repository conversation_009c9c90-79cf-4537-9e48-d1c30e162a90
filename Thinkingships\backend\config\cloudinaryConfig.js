const cloudinary = require('cloudinary').v2;

cloudinary.config({
    cloud_name:process.env.CLOUDINARY_NAME,
    api_key:process.env.CLOUDINARY_API_KEY,
    api_secret:process.env.CLOUDINARY_API_SECRET
})

const extractPublicId = (url) => {
    try{
    const parts = url.split('/');
    const fileWithExtension = parts[parts.length - 1]; // abc123xyz.jpg
    const fileName = fileWithExtension.split('.')[0];  // abc123xyz
    const folderName = parts[parts.length - 2];        // posts or uploads/...

    return `${folderName}/${fileName}`; // e.g., posts/abc123xyz
    }catch(error){
        console.log("error in cloudinary config while finding the public url ",error);
        return null;
    }

};

// return type - true/false
const deleteFromCloudinary = async(url='')=>{
    try{
        if(!url){
            console.log("no url was found to delete from the cloudinary ---- returning false");
            return false;
        }
        if (!url.startsWith('https://res.cloudinary.com/')) {
            console.log("The image is not in cloudinary yet");
            return true;
        }
        const public_id = extractPublicId(url);
        if(public_id){
            console.log("deleting url from cloudinary ",url,"\nof folder",public_id);
            await cloudinary.uploader.destroy(public_id);
            return true;
        }
        else{
            return false;
        }
    }catch(error){
        console.log("Something went wrong while destroying the file from cloudinary",error);
        return false;
    }
}

// return type uploaded-url / false
const uploadToCloudinary = async(url='',folderName = 'default', resource_type = 'image' )=>{
    try{
        if(!url){
            console.log("No url was found to upload returning false");
            return false;
        }
        const uploadedResponse = await cloudinary.uploader.upload(url,{
            folder:folderName,
            resource_type:resource_type
        })
        const return_type = uploadedResponse?.secure_url || false;
        return return_type; 
    }catch(error){
        console.log("Something went wrong while destroying the file from cloudinary",error);
        return false;
    }
}

module.exports = {uploadToCloudinary,deleteFromCloudinary};