const catchAsyncError = require('../middleware/catchAsyncError')
const { generateUUID } = require('../utils/generateIDS')
const ErrorHandler = require('../errors/ErrorHandler')
const pool = require('../config/sqlConnector')
const {
    uploadToCloudinary,
    deleteFromCloudinary
} = require('../config/cloudinaryConfig')

const LIMIT = process.env.PAGE_LIMIT

// calculations
function calculateAmtToReceive(userQuote) {
    if (typeof userQuote !== 'number' || isNaN(userQuote)) {
        throw new Error("Invalid userQuote value");
    }
    const feePercentage = 0.05;
    const amtToReceive = userQuote - (userQuote * feePercentage);
    return parseFloat(amtToReceive.toFixed(2)); // limit to 2 decimal places
}


// publication ---------------------------------------------
// this will be created once and if someone saves it; it will ask the user to go to drafts and continue
exports.createAPitch = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id
    const id = generateUUID()

    const {
        title,
        brief,
        contentType,
        genre,
        deadline,
        minWordCount,
        maxWordCount,
        language,
        tags,
        submissionGuidelines,
        publicationRightCheck = false,
        showNumberOfSubmissionCheck = false,
        AutoCloseAfterDeadLine = false,
        savedAsDraft = true,
        setLive = false
    } = req.body

    if (!title || !contentType) {
        return next(new ErrorHandler('Title and contentTypes are missing', 400))
    }

    const data = [
        id,
        userId,
        title,
        brief || null,
        contentType,
        genre || null,
        deadline || null,
        minWordCount || null,
        maxWordCount || null,
        language || null,
        tags || null,
        submissionGuidelines || null,
        publicationRightCheck,
        showNumberOfSubmissionCheck,
        AutoCloseAfterDeadLine,
        savedAsDraft,
        false,
        setLive
    ]

    const query = `
    INSERT INTO pitch (
        id, userId, title, brief, contentType, genre, deadline,
        minWordCount, maxWordCount, languages, tags, submissionGuidelines,
        publicationRightCheck, showNumberOfSubmissionCheck, AutoCloseAfterDeadLine,
        savedAsDraft, pitchClosed, setLive
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    await pool.query(query, data)

    const [rows] = await pool.query(`SELECT * FROM pitch WHERE id = ?`, [id])
    const response = rows[0]

    return res.status(200).json({
        success: true,
        pitch: response
    })
})

exports.closePitch = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id
    const { pitchId } = req.params

    const [rows] = pool.query('Select * from pitch where id = ? AND userId = ?', [pitchId, userId])
    let data = rows[0] || null;

    if (!data) {
        return next(new ErrorHandler('Pitch not found', 404))
    }

    let message
    if (data.pitchClosed === 1 || data.pitchClosed === true) {
        message = 'Already Closed'
    } else {
        await pool.query('UPDATE pitch SET pitchClosed = ? WHERE id = ?', [
            true,
            pitchId
        ])
        message = 'Pitch closed successfully'
        data.pitchClosed = true
    }

    return res.status(200).json({
        success: true,
        pitch: data,
        message: message
    })
})

exports.shareFeedbackToAuthors = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const { pitchId } = req.params;
    const { authorId, feedBack } = req.body;

    const [[submissionRows], [pitchRows]] = await Promise.all([
        pool.query(
            "SELECT * FROM pitchSubmissions WHERE pitchId = ? AND userId = ?",
            [pitchId, authorId]
        ),
        pool.query(
            "SELECT * FROM pitch WHERE id = ? AND userId = ?",
            [pitchId, userId]
        )
    ]);

    let submissionData = submissionRows[0] || null;
    let pitchData = pitchRows[0] || null;

    if (!pitchData || !submissionData) {
        return next(new ErrorHandler("Pitch or Submission not found", 400));
    }

    if (pitchData.pitchClosed) {
        return next(new ErrorHandler("Pitch is already closed", 400));
    }

    if (submissionData.feedBack?.toString().trim().length > 0) {
        return next(new ErrorHandler("Feedback already exists", 400));
    }

    await pool.query(
        "UPDATE pitchSubmissions SET feedBack = ? WHERE pitchId = ? AND userId = ?",
        [feedBack, pitchId, authorId]
    );

    submissionData.feedBack = feedBack;

    return res.status(200).json({
        success: true,
        submissionData
    });
});

exports.acceptOrRejectAPitch = catchAsyncError(async (req, res, next) => {
    // accept or reject any submission -> on acceptance include payment
    const userId = req.user.id;
    const { pitchId } = req.params;
    const { authorId, status } = req.body;

    const [[submissionRows], [pitchRows]] = await Promise.all([
        pool.query(
            "SELECT * FROM pitchSubmissions WHERE pitchId = ? AND userId = ?",
            [pitchId, authorId]
        ),
        pool.query(
            "SELECT * FROM pitch WHERE id = ? AND userId = ?",
            [pitchId, userId]
        )
    ]);

    let submissionData = submissionRows[0] || null;
    let pitchData = pitchRows[0] || null;

    if (!pitchData || !submissionData) {
        return next(new ErrorHandler("Pitch or Submission not found", 400));
    }

    if (pitchData.pitchClosed) {
        return next(new ErrorHandler("Pitch is already closed", 400));
    }

    if (status === 'accept' || status === 'pending' || status === 'rejected') return next(new ErrorHandler("Invalid status", 400));

    if (status === 'accept') {
        //  have to add payment gateway and features over here...
    }

    await pool.query("Update pitchSubmissions set submissionStatus = ? where id = ?", [status, submissionData.id]);
    submissionData.submissionStatus = status;

    return res.status(200).json({
        success: true,
        submissionData
    })
})

exports.fetchPublicationPitch = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 0; // default page 0
    const offset = page * LIMIT;

    const [rows] = await pool.query(
        `SELECT * FROM pitch 
        WHERE userId = ? 
        ORDER BY createdAt DESC 
        LIMIT ? OFFSET ?`,
        [userId, LIMIT, offset]
    );

    res.status(200).json({
        success: true,
        length: rows.length,
        pitch: rows
    });
});


// this is to show all the submissions done by authors
exports.showAllSubmissions = catchAsyncError(async (req, res, next) => {
    const userId = req.user.id;
    const { pitchId } = req.params;
    const page = parseInt(req.query.page) || 0; // default page 0
    const offset = page * LIMIT;
    const [rows] = await pool.query(
        "Select ps.* from pitch as p left join pitchSubmissions as ps on p.id = ps.pitchId where p.id = ? AND ps.pitchWithDrawn = False AND isPublished=TRUE And p.userId = ? order by createdAt desc LIMIT ? OFFSET ?", 
        [pitchId, userId, LIMIT, offset]);

    return res.status(200).json({ success: true, length: rows.length, submissions: rows });
})

// Author----------------------------------------------------
exports.answerAPitch = catchAsyncError(async (req, res, next) => { 
    // need to implement payment logic in this controller before submission
    const userId = req.user.id;
    const { pitchId } = req.params;

    // Checking if pitch already exists
    const [rows] = await pool.query(
        "SELECT * FROM pitchSubmissions WHERE userId = ? AND pitchId = ?",
        [userId, pitchId]
    );
    if (rows.length > 0) {
        return next(new ErrorHandler("You have already submitted once", 400));
    }

    const {
        userQuote,
        title,
        draft,
        coverImage,
        isPublished = false
    } = req.body;

    if (!userQuote || !title || !draft || !coverImage) {
        return next(new ErrorHandler("Missing required fields", 400));
    }

    const AmtToReceive = calculateAmtToReceive(userQuote);
    // Uploading requirements url='',folderName = 'default', resource_type = 'image'
    const imgUrl = uploadToCloudinary(coverImage,'pitch','image')
    if(!imgUrl) return next(new ErrorHandler("Something went wrong while uploading the picture",400));

    const data = [
        userId,
        pitchId,
        userQuote,
        AmtToReceive,
        title,
        draft,
        imgUrl,
        isPublished
    ];

    const query = `
        INSERT INTO pitchSubmissions (
            userId, pitchId, userQuote, AmtToReceive, title, draft, coverImage, isPublished
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await pool.query(query, data);

    return res.status(200).json({ success: true, message: "Pitch submitted successfully." });
});


exports.reviseAPitchAnswer = catchAsyncError(async(req, res, next) => { 
    const userId = req.user.id;
    const { id: pitchAnswerId } = req.params;
    // Checking if pitch already exists
    const [rows] = await pool.query(
        "SELECT * FROM pitchSubmissions WHERE id = ? and userId = ? ",
        [pitchAnswerId,userId]
    );
    
    if(rows.length === 0) return next(new ErrorHandler("Pitch Submission not found",400));

    const {userQuote,} = req.body;
    calculateAmtToReceive(userQuote);
    
    await pool.query(`update `)


})

exports.withDrawAPitchSubmission = catchAsyncError(async(req,res,next)=>{
    const {pitchId} = req.params;
    const userId = req.id;

    const [rows] = await pool.query(`Select * from pitchSubmission where id = ? and userId = ? `,[pitchId,userId]);

    if(!rows) return next(new ErrorHandler("pitch not found",400));

    await pool.query(`update pitchSubmission set pitchWithDrawn=TRUE where userId = ? and pitchId = ?`,[userId,pitchId]);

    res.status(200).json({success:true,message:"status updated successfully"});
})
