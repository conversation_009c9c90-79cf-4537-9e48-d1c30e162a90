import React, { useState } from 'react';
import toast from 'react-hot-toast';
import SkriveeHandling from '../../action/SkriveeHandling';

const genres = ['Mystery', 'Romance', 'Thriller', 'Sci-Fi', 'Fantasy'];
const languages = ['English', 'Hindi', 'Spanish', 'French'];

function Skrivee() {
  const { createSkrivee, skriveeLoading, updateSkrivee } = SkriveeHandling();
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    skriveeTxt: '',
    abstract: '',
    genre: '',
    tags: '',
    deticatedTo: '',
    ReadingLevel: '',
    languages: '',
    adultContent: false,
    subscription: false,
    isPublished: false,
    story_cover: null,
  });

  const [previewImage, setPreviewImage] = useState(null);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const isValid = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isValid) {
      toast.error('Please upload only JPEG or PNG images');
      return;
    }

    setFormData((prev) => ({
      ...prev,
      story_cover: file,
    }));
    setPreviewImage(URL.createObjectURL(file));
  };

  const handleSubmit = (e, publish = false) => {
    e.preventDefault();

    const payload = new FormData();
    Object.entries({ ...formData, isPublished: publish }).forEach(([key, val]) => {
      payload.append(key, val);
    });

    createSkrivee(payload);
  };

  return (
    <div className='flex flex-col md:flex-row gap-8 mt-6 w-full p-4'>
      <form onSubmit={(e) => handleSubmit(e, false)} className="flex flex-col md:flex-row gap-8 w-full">
        {/* Left Section */}
        <div className="w-full md:w-1/2 flex flex-col gap-4">
          <input
            name="title"
            type="text"
            placeholder="Title"
            className="p-2 border rounded"
            value={formData.title}
            onChange={handleChange}
            required
          />

          <textarea
            name="skriveeTxt"
            placeholder="Write your story here..."
            className="p-2 border rounded h-40 resize-none"
            value={formData.skriveeTxt}
            onChange={handleChange}
            required
          />
          <div className="text-sm text-gray-600">
            Character Count: {formData.skriveeTxt.length}
          </div>

          <textarea
            name="abstract"
            placeholder="Abstract (max 300 chars)"
            maxLength={300}
            className="p-2 border rounded"
            value={formData.abstract}
            onChange={handleChange}
          />

          <select name="genre" className="p-2 border rounded" value={formData.genre} onChange={handleChange}>
            <option value="">Select Genre</option>
            {genres.map((g) => <option key={g} value={g}>{g}</option>)}
          </select>

          <input
            name="tags"
            type="text"
            placeholder="Tags (comma separated)"
            className="p-2 border rounded"
            value={formData.tags}
            onChange={handleChange}
          />

          <input
            name="deticatedTo"
            type="text"
            placeholder="Dedicated To"
            className="p-2 border rounded"
            value={formData.deticatedTo}
            onChange={handleChange}
          />

          <input
            name="ReadingLevel"
            type="text"
            placeholder="Reading Level"
            className="p-2 border rounded"
            value={formData.ReadingLevel}
            onChange={handleChange}
          />

          <select name="languages" className="p-2 border rounded" value={formData.languages} onChange={handleChange}>
            <option value="">Select Language</option>
            {languages.map((lang) => <option key={lang} value={lang}>{lang}</option>)}
          </select>
        </div>

        {/* Right Section */}
        <div className="w-full md:w-1/2 flex flex-col gap-4">
          <label className="font-semibold">Set a tone: Attach a picture</label>
          <input
            name="story_cover"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
          />
          {previewImage && (
            <img src={previewImage} alt="Preview" className="h-40 w-40 object-cover rounded" />
          )}
          <p className="text-xs text-gray-500">
            Studies show story covers improve engagement. Recommended: 1080x1080px, max 5MB.
          </p>

          <div className="flex items-center gap-2">
            <label>18+ Content:</label>
            <input
              type="checkbox"
              name="adultContent"
              checked={formData.adultContent}
              onChange={handleChange}
            />
          </div>

          <div className="flex items-center gap-2">
            <label>Subscription Required:</label>
            <input
              type="checkbox"
              name="subscription"
              checked={formData.subscription}
              onChange={handleChange}
            />
          </div>

          <div className="flex gap-4 mt-4">
            <button type="submit" className="bg-gray-500 text-white px-4 py-2 rounded">
              Save Draft
            </button>
            <button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              className="bg-blue-600 text-white px-4 py-2 rounded"
            >
              Publish
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}

export default Skrivee;
