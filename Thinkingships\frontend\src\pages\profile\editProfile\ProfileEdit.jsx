import React, { useState } from 'react'
import BusinessEdit from './BusinessEdit';
import PublicationEdit from './PublicationEdit';
import AuthorEdit from './AuthorEdit';
import { useSelector } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { useEffect } from 'react';

function ProfileEdit() {
    const { user } = useSelector((state) => state.user);
    const currentRole = user.userRole || "author";
    if (!user) return;
    if (currentRole === "author") return (
        <div className='min-h-screen h-full'>
            <AuthorEdit user={user} />
            <Toaster
                position="top-center"
                reverseOrder={false}
            />
        </div>
    )
    if (currentRole === "publication") return (
        <>
            <PublicationEdit user={user} />
            <Toaster
                position="top-center"
                reverseOrder={false}
            />
        </>
    )
    if (currentRole === "business") return (
        <>
            <BusinessEdit user={user} />
            <Toaster
                position="top-center"
                reverseOrder={false}
            />
        </>
    )
}

export default ProfileEdit