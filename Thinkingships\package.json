{"name": "backend", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node backend/server.js", "dev": "nodemon backend/server.js"}, "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "html-to-text": "^9.0.5", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "socket.io": "^4.8.1", "uuid": "^11.1.0"}}