const transport = require("../config/emailConfig")
const ErrorHandler = require("../errors/ErrorHandler")
const catchAsyncError = require("../middleware/catchAsyncError")
const {convert} = require('html-to-text')

const emailService = catchAsyncError (async({receiverId,subject,html,includeFooter=true}) =>{
    // Adding autogenerated notice in html body
    if(includeFooter) 
        html += `
        <br><br>
        <div style="font-size: 14px; color: #555;">
            <em>This is an auto-generated email. Please do not reply to this message.</em><br>
            For any assistance, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
        </div>
        `;

    // converting html to simple text for fall back cases where emails doesn't support html
    let text = convert(html);

    // Setting up mail configurations
    const mailOptions = {
        from : process.env.NODEMAILER_EMAIL_ID,
        to: receiverId,
        subject:subject,
        text:text,
        html:html
    }
    console.log("Mail options -> ",mailOptions,"\nSending to : ",receiverId);
    
    // sending email
    // const timeoutMs = parseInt(process.env.EMAIL_TIMEOUT) || 20000; // default to 20s
    const timeoutMs = 600000; // default to 20s
    const sendWithTimeOut = new Promise((resolve,reject)=>{
        const timeout = setTimeout(()=>{
            reject(new Error("Email sending timeout......"))
        },timeoutMs);   
        
        transport.sendMail(mailOptions,(err,info)=>{
            clearTimeout(timeout); // removing the timer since we got the response
            if(err) return reject(err);
            resolve(info);
        })
    })
    try{
        const info = await sendWithTimeOut;
        console.log("Mail send successfully to ------ ",receiverId);
        return true;
    }catch(error){
        console.log("Error while sending the mail",error);
        return false;
    }
})

module.exports = emailService;