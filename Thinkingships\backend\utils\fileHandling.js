// handling multi-form data
const multer = require("multer");
const ErrorHandler = require("../errors/ErrorHandler");
const storage = multer.memoryStorage();
const MAX_IMAGE_SIZE = process.env.IMAGESIZE * 1024 * 1024 // 5 mb at max rn
const MAX_AUDIO_SIZE = process.env.AUDIOSIZE * 1024 * 1024 // 15 mb at max rn

const validateFileSize = (req, res, next) => {
    const file = req.file;
    if (!file) return next();

    const isImage = file.mimetype.startsWith("image/");
    const isAudio = file.mimetype.startsWith("audio/");

    if (isImage && file.size > MAX_IMAGE_SIZE) {
        return next(new ErrorHandler("Image size exceeds 5MB", 400))
    }

    if (isAudio && file.size > MAX_AUDIO_SIZE) {
        return next(new <PERSON>rrorHand<PERSON>("Audio size exceeds 15MB", 400))
    }
    req.file.base64 = `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
    return next();
};


const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype.startsWith("image/") ||
            file.mimetype.startsWith("audio/")
        ) {
            cb(null, true);
        } else {
            return cb(new ErrorHandler("Only image or audio files are allowed!", 400));
        }
    },
});


module.exports = { validateFileSize, upload };