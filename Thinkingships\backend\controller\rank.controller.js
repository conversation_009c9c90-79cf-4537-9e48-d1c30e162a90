const pool = require('../config/sqlConnector');
const catchAsyncError = require('../middleware/catchAsyncError');

const map = {
    "short story": 8,
    "poem": 6,
    "article/blog": 10,
    "e-book": 20,
};

exports.increaseRank = catchAsyncError(async (rankType, content_type, userId) => {
    const content = content_type.toLowerCase();
    const scoreToAdd = map[content] ?? 0;

    await pool.query(
        `INSERT INTO ranks (userId, rankType, score) 
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE score = score + VALUES(score)`,
        [userId, rankType, scoreToAdd]
    );
});

exports.showRankPublication = catchAsyncError(async (req, res, next) => {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const [rows] = await pool.query(
        `SELECT score, rankType FROM ranks WHERE rankType = 'publication' ORDER BY score DESC LIMIT ? OFFSET ?`,
        [limit, offset]
    );
    const result = rows || [];
    return res.status(200).json({ success: true, ranks: result });
});

exports.showRankAuthor = catchAsyncError(async (req, res, next) => {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const [rows] = await pool.query(
        `SELECT score, rankType FROM ranks WHERE rankType = 'author' ORDER BY score DESC LIMIT ? OFFSET ?`,
        [limit, offset]
    );
    const result = rows || [];
    return res.status(200).json({ success: true, ranks: result });
});
