import axios from "axios";
import {
    clearErrors,
    fetchMeFail,
    fetchMeRequest,
    fetchMeSuccess,
    loginFail,
    loginRequest,
    loginSuccess,
    logoutFail,
    logoutRequest,
    logoutSuccess,
    registerFail,
    registerRequest,
    registerSuccess
} from "../reducer/userReducer"
import toast from "react-hot-toast";
import cookie from 'js-cookie'
import { useNavigate } from "react-router-dom";

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;
const SERVER = import.meta.env.VITE_ENV_TYPE;

const config_application_json = {
    headers: {
        'Content-Type': 'application/json'
    },
    timeout: 7000
    // timeout set for 7s
}

export const login = (formData) => async (dispatch) => {
    try {
        dispatch(loginRequest());
        const res = await axios.post(
            `${BACKEND_URL}/v1/user/login`,
            formData,
            config_application_json
        );
        dispatch(loginSuccess(res.data.user || {}))
        if(res?.data?.token) {
            cookie.set("token", res.data.token, {
                expires: 2,
                secure: SERVER === 'production',
                sameSite: 'strict',
            });
        }
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            dispatch(loginFail("Request timed out. Please try again."));
            toast.error("Timeout error")
        } else {
            dispatch(loginFail(error?.response?.data?.err ?? "Login Failed"));
            toast.error(error?.response?.data?.err ?? "Something went wrong", { duration: 3000 })
        }
    } finally {
        dispatch(clearErrors());
    }
}

export const logout = () => async (dispatch) => {
    try {
        dispatch(logoutRequest());
        const res = await axios.get(
            `${BACKEND_URL}/v1/user/logout`,
            {withCredentials: true},
            config_application_json
        );
        dispatch(logoutSuccess())
        cookie.remove("token", {
        path: '/',
        secure: SERVER === 'production',
        sameSite: 'strict',
        });

    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            dispatch(loginFail("Request timed out. Please try again."));
            toast.error("Timeout error")
        } else {
            dispatch(logoutFail(error?.response?.data?.message ?? "Login Failed"));
        }
    } finally {
        dispatch(clearErrors());
    }
}

export const registerUser = (formData, type) => async (dispatch) => {
    try {
        dispatch(registerRequest());
        const res = await axios.post(
            `${BACKEND_URL}/v1/user/create/${type}`,
            formData,
            config_application_json
        );
        dispatch(registerSuccess(res.data.user || {}))

        if(res?.data?.token) {
            cookie.set("token", res.data.token, {
                expires: 2,
                secure: SERVER === 'production',
                sameSite: 'strict',
            });
        }
    } catch (error) {
        if (error.code === 'ECONNABORTED') {
            dispatch(loginFail("Request timed out. Please try again."));
            toast.error("Timeout error")
        } else {
            dispatch(registerFail(error?.response?.data?.message ?? "Register Failed"));
            toast.error(error?.response?.data?.err, { duration: 2000 })
        }
    } finally {
        dispatch(clearErrors());
    }
}

export const fetchMe = () => async(dispatch) => {
    try{
        dispatch(fetchMeRequest());
        const res = await axios.get(
            `${BACKEND_URL}/v1/user/me`, 
            {withCredentials: true},
            config_application_json,
            
        );
        dispatch(fetchMeSuccess(res.data.user || {}))
    }catch(error){
        if (error.code === 'ECONNABORTED') {
            dispatch(fetchMeFail("Request timed out. Please try again."));
            toast.error("Timeout error")
        } else {
            dispatch(fetchMeFail(error?.response?.data?.message ?? "Something went wrong"));
            toast.error(error?.response?.data?.err, { duration: 2000 })
        }
        cookie.remove("token", {
        path: '/',
        secure: SERVER === 'production',
        sameSite: 'strict',
        });
    }finally{
        dispatch(clearErrors());
    }
}

export const updateUser = () => async(dispatch) => {
    try{
        dispatch(fetchMeRequest());
        const res = await axios.get(
            `${BACKEND_URL}/v1/user/me`, 
            {withCredentials: true},
            config_application_json,
            
        );
        dispatch(fetchMeSuccess(res.data.user || {}))
    }catch(error){
        if (error.code === 'ECONNABORTED') {
            dispatch(fetchMeFail("Request timed out. Please try again."));
            toast.error("Timeout error")
        } else {
            dispatch(fetchMeFail(error?.response?.data?.message ?? "Something went wrong"));
            toast.error(error?.response?.data?.err, { duration: 2000 })
        }
    }finally{
        dispatch(clearErrors());
    }
}