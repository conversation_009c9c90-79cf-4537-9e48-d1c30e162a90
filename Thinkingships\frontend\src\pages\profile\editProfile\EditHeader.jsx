import React from 'react';

function EditHeader({ user, txt = "author" }) {
  return (
    <div className="flex flex-col items-start justify-start text-start mt-6">
      {/* Greeting */}
      <h1 className="text-2xl font-semibold text-gray-700">
        Edit Profile, {user} 
        <button className="ml-2 px-3 py-1 bg-[#4A99F8] text-white text-sm rounded-full shadow-sm">
          {txt}
        </button>
      </h1>

      {/* Decorative Line */}
      <div className="flex items-center justify-center mt-4 gap-2">
        <div className="w-32 h-1 rounded-r-full bg-[#D99FE9]" />
        <div className="text-gray-400 text-xl font-bold">•</div>
        <div className="w-32 h-1 rounded-l-full bg-[#4A99F8]" />
      </div>
    </div>
  );
}

export default EditHeader;
