const express = require("express");
const {Server} = require("socket.io")
const http = require("http")
const cors = require('cors')
const app = express();
const cookieParser = require('cookie-parser')

// enabling json parsing
app.use(express.json({limit:'20mb'}));
app.use(express.urlencoded({extended:true,limit:'20mb'})) 
app.use(cookieParser(process.env.COOKIE_SECRET));

app.use(express.json())
// http server for socket
const server = http.createServer(app);

// setting up cors for api and socket access from frontend
app.use(cors({
    origin:[process.env.FRONTEND_URL,"http://192.168.29.160:5173","http://192.168.1.33:5173","http://192.168.29.160:5173","http://192.168.1.34:5173"],
    credentials: true
}))
const io = new Server(server,{
    cors: {
        origin: [process.env.FRONTEND_URL,"http://192.168.29.160:5173","http://192.168.1.33:5173","http://192.168.29.160:5173","http://192.168.1.34:5173"],
        methods: ["GET","POST","PUT","DELETE"],
        allowedHeaders: ["Content-Type", "Authorization"],
        credentials : true
    },
    transports:["websocket","polling"]
})

module.exports = {
    io,
    server,
    app
}
