import axios from 'axios';
import React from 'react'
import { useState } from 'react'
import toast from 'react-hot-toast';
const BACKEND_URL =  import.meta.env.VITE_BACKEND_URL;
const config_application_json = {
    headers: {
        'Content-Type': 'application/json',
    },
    withCredentials: true,
    timeout: import.meta.env.VITE_API_TIMEOUT
    // timeout set for 7s
}
function TokenHandling() {
    const [tokenLoading,setTokenLoading] = useState(false);
    const resendTokenReq = async() => {
        try{
            setTokenLoading(true);
            await axios.put(`${BACKEND_URL}/v1/user/resend-token`,{},config_application_json);
            return true;
        }catch(error){
            if (error.code === 'ECONNABORTED') {
                toast.error("Timeout error")
            } else {
                toast.error(error?.response?.data?.err ?? "Something went wrong", { duration: 3000 })
            }
            return false;
        }finally{
            setTokenLoading(false);
        }
    }

    return {resendTokenReq,tokenLoading};
}

export default TokenHandling