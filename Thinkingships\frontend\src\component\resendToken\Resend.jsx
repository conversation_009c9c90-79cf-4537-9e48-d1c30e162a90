import React, { useState } from 'react'
import { useDispatch } from 'react-redux';

function Resend() {
    const dispatch = useDispatch();
    const [emailLoading,setEmailLoading] = useState(false)
    const [email,setEmail] = useState('')
    const handleSubmit = (e) => {
        e.preventDefault();

    }
  return (
    <div><form onSubmit={handleSubmit}>
                    <input
                    type="email"
                    placeholder="Email / User-name"
                    autoComplete="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-2 mb-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                    {emailLoading ? (
                    <button
                        type="button"
                        disabled
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md flex items-center justify-center gap-2 cursor-default opacity-75"
                    >
                        <svg
                            className="animate-spin h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            ></circle>
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                            ></path>
                        </svg>
                        Resending...
                    </button>
                ) : (
                    <button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition duration-300"
                    >
                        Resend
                    </button>
                )}
                    </form>
                    </div>
  )
}

export default Resend