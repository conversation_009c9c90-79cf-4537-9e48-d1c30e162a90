import axios from "axios";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams, useNavigate } from "react-router-dom";
import { fetchMe } from "../../action/userActions";

const verificationStatus = {
  loading: {
    message: "Verifying your email. Please wait a moment...",
    text_color: "text-gray-700",
    animation: (
      <svg className="animate-spin h-10 w-10 text-blue-600" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
      </svg>
    )
  },
  success: {
    message: "Your email has been successfully verified. You can now log in to your account.",
    text_color: "text-green-500",
    animation: (
      <svg className="h-16 w-16 text-green-500" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
      </svg>
    )
  },
  error: {
    message: "Verification failed. The token may be invalid or expired.",
    text_color: "text-red-500",
    animation: (
      <svg className="h-12 w-12 text-red-500" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
    )
  },
  already_verified: {
    message: "Your email is already verified. You can continue using your account.",
    text_color: "text-blue-600",
    animation: (
      <svg className="h-16 w-16 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M12 20h.01M4 4l16 16" />
      </svg>
    )
  },
  not_logged_in: {
    message: "You are not logged in. Please log in to verify your email.",
    text_color: "text-yellow-600",
    animation: (
      <svg className="h-14 w-14 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M12 11V7m0 4v4m0-4h4m-4 0H8m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  }
};

function Verification() {
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const [status, setStatus] = useState("loading");
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    if (!user) {
      setStatus("not_logged_in");
      return;
    }

    if (user?.isVerified) {
      setStatus("already_verified");
      return;
    }

    if (!token) {
      setStatus("error");
      toast.error("Token missing from URL.");
      return;
    }

    axios
      .put(`${import.meta.env.VITE_BACKEND_URL}/v1/user/verify-email/${token}`, {}, { withCredentials: true })
      .then(() => {
        setStatus("success");
        toast.success("Email verified! Please continue.");
        dispatch(fetchMe());
      })
      .catch((err) => {
        const msg =
          err?.response?.data?.err ||
          err.message ||
          "Token is invalid or expired. Please try again.";
        toast.error(msg);
        setStatus("error");
      });
  }, []);

  const current = verificationStatus[status];
  console.log(current)

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#F8F8F8] relative overflow-hidden px-4">
      <div className="absolute right-[-150px] bottom-[-50px] transform w-[400px] h-[400px] bg-blue-200 rounded-full z-0" />
      <div className="absolute left-[-100px] top-1 transform -translate-y-1/2 w-[400px] h-[400px] bg-blue-200 rounded-full z-0" />

      <div className="flex flex-col items-center justify-center h-[20rem] bg-white bg-opacity-90 backdrop-blur-md p-6 rounded-xl shadow-md z-10 w-full max-w-sm text-center mb-2">
        {current?.animation}
        <p className={`mt-4 text-lg ${current?.text_color}`}>{current?.message}</p>
      </div>
    </div>
  );
}

export default Verification;
