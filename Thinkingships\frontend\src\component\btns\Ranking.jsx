import React from 'react'
const ranking = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M13.5 12H16.5C18.621 12 19.6815 12 20.34 12.66C21 13.3185 21 14.379 21 16.5V25.5C21 23.379 21 22.3185 21.66 21.66C22.317 21 23.3775 21 25.5 21C27.6225 21 28.6815 21 29.34 21.66C30 22.3185 30 23.379 30 25.5V30H0C0 27.879 8.9407e-08 26.8185 0.66 26.16C1.317 25.5 2.3775 25.5 4.5 25.5C6.6225 25.5 7.6815 25.5 8.34 26.16C9 26.817 9 27.8775 9 30V16.5C9 14.379 9 13.3185 9.66 12.66C10.317 12 11.3775 12 13.5 12ZM13.719 1.5345C14.289 0.51 14.574 0 15 0C15.426 0 15.711 0.51 16.281 1.5345L16.428 1.7985C16.59 2.0895 16.671 2.2335 16.797 2.3295C16.9245 2.4255 17.082 2.4615 17.397 2.532L17.682 2.598C18.789 2.8485 19.3425 2.973 19.4745 3.396C19.6065 3.819 19.2285 4.2615 18.474 5.1435L18.279 5.3715C18.0645 5.622 17.9565 5.7465 17.9085 5.9025C17.8605 6.0585 17.877 6.225 17.9085 6.5595L17.9385 6.864C18.0525 8.0415 18.1095 8.631 17.766 8.892C17.421 9.1545 16.902 8.9145 15.8655 8.4375L15.5985 8.3145C15.303 8.1795 15.156 8.1105 15 8.1105C14.844 8.1105 14.697 8.1795 14.4015 8.3145L14.1345 8.4375C13.098 8.9145 12.579 9.1545 12.234 8.892C11.889 8.631 11.9475 8.0415 12.0615 6.864L12.0915 6.5595C12.123 6.225 12.1395 6.0585 12.0915 5.9025C12.0435 5.7465 11.9355 5.622 11.721 5.3715L11.526 5.1435C10.7715 4.2615 10.3935 3.8205 10.5255 3.396C10.6575 2.973 11.211 2.8485 12.318 2.598L12.603 2.532C12.918 2.4615 13.0755 2.427 13.203 2.3295C13.329 2.2335 13.41 2.0895 13.572 1.7985L13.719 1.5345Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}

function Ranking({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M13.5 12H16.5C18.621 12 19.6815 12 20.34 12.66C21 13.3185 21 14.379 21 16.5V25.5C21 23.379 21 22.3185 21.66 21.66C22.317 21 23.3775 21 25.5 21C27.6225 21 28.6815 21 29.34 21.66C30 22.3185 30 23.379 30 25.5V30H0C0 27.879 8.9407e-08 26.8185 0.66 26.16C1.317 25.5 2.3775 25.5 4.5 25.5C6.6225 25.5 7.6815 25.5 8.34 26.16C9 26.817 9 27.8775 9 30V16.5C9 14.379 9 13.3185 9.66 12.66C10.317 12 11.3775 12 13.5 12ZM13.719 1.5345C14.289 0.51 14.574 0 15 0C15.426 0 15.711 0.51 16.281 1.5345L16.428 1.7985C16.59 2.0895 16.671 2.2335 16.797 2.3295C16.9245 2.4255 17.082 2.4615 17.397 2.532L17.682 2.598C18.789 2.8485 19.3425 2.973 19.4745 3.396C19.6065 3.819 19.2285 4.2615 18.474 5.1435L18.279 5.3715C18.0645 5.622 17.9565 5.7465 17.9085 5.9025C17.8605 6.0585 17.877 6.225 17.9085 6.5595L17.9385 6.864C18.0525 8.0415 18.1095 8.631 17.766 8.892C17.421 9.1545 16.902 8.9145 15.8655 8.4375L15.5985 8.3145C15.303 8.1795 15.156 8.1105 15 8.1105C14.844 8.1105 14.697 8.1795 14.4015 8.3145L14.1345 8.4375C13.098 8.9145 12.579 9.1545 12.234 8.892C11.889 8.631 11.9475 8.0415 12.0615 6.864L12.0915 6.5595C12.123 6.225 12.1395 6.0585 12.0915 5.9025C12.0435 5.7465 11.9355 5.622 11.721 5.3715L11.526 5.1435C10.7715 4.2615 10.3935 3.8205 10.5255 3.396C10.6575 2.973 11.211 2.8485 12.318 2.598L12.603 2.532C12.918 2.4615 13.0755 2.427 13.203 2.3295C13.329 2.2335 13.41 2.0895 13.572 1.7985L13.719 1.5345Z" fill={color} />
            </svg>
    </div>
    <div className="hidden md:flex">
        {ranking(color,h,w,txt)}
    </div>
    </>
  )
}

export default Ranking