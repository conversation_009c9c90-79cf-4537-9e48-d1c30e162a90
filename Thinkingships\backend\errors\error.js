// const ErrorHandler = require('./ErrorHandler');

module.exports = (err,req,res,next) => {
    // err code and message
    err.statusCode = err.statusCode || 500;
    err.message = err.message || `Internal Server Error`;

    // logging error
    console.log(`Error ----------\n${err.message}\n${err.message}`); 

    // returning the error response
    res.status(err.statusCode).json({
        success:false,
        err:err.message,
        error:err.stack
    })
}