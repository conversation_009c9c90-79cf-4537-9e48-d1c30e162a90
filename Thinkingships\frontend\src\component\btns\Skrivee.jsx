import React from 'react'
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M28.4278 0.740671L29.2506 1.55458C30.3843 2.6775 30.2128 4.66813 28.8643 6.00073L11.8969 22.7852L6.39956 24.7744C5.70926 25.0255 5.03709 24.6999 4.90043 24.0502C4.85439 23.8145 4.87613 23.5707 4.96318 23.3466L7.01316 17.8617L23.9332 1.12279C25.2817 -0.209809 27.294 -0.382247 28.4278 0.740671ZM11.1564 2.40987C11.3395 2.40987 11.5208 2.44556 11.69 2.51488C11.8592 2.58421 12.0129 2.68582 12.1424 2.81392C12.2719 2.94202 12.3747 3.0941 12.4447 3.26147C12.5148 3.42883 12.5509 3.60822 12.5509 3.78938C12.5509 3.97054 12.5148 4.14992 12.4447 4.31729C12.3747 4.48466 12.2719 4.63674 12.1424 4.76484C12.0129 4.89294 11.8592 4.99455 11.69 5.06388C11.5208 5.1332 11.3395 5.16889 11.1564 5.16889H5.57817C4.83846 5.16889 4.12905 5.45957 3.60599 5.97698C3.08294 6.4944 2.78909 7.19616 2.78909 7.9279V24.482C2.78909 25.2137 3.08294 25.9155 3.60599 26.4329C4.12905 26.9503 4.83846 27.241 5.57817 27.241H22.3127C23.0524 27.241 23.7618 26.9503 24.2849 26.4329C24.8079 25.9155 25.1018 25.2137 25.1018 24.482V18.9639C25.1018 18.5981 25.2487 18.2472 25.5102 17.9885C25.7718 17.7298 26.1265 17.5844 26.4963 17.5844C26.8662 17.5844 27.2209 17.7298 27.4824 17.9885C27.744 18.2472 27.8909 18.5981 27.8909 18.9639V24.482C27.8909 25.9454 27.3032 27.349 26.2571 28.3838C25.211 29.4186 23.7921 30 22.3127 30H5.57817C4.09875 30 2.67992 29.4186 1.63381 28.3838C0.587699 27.349 0 25.9454 0 24.482V7.9279C0 6.46443 0.587699 5.0609 1.63381 4.02607C2.67992 2.99124 4.09875 2.40987 5.57817 2.40987H11.1564Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}
function Skrivee({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M28.4278 0.740671L29.2506 1.55458C30.3843 2.6775 30.2128 4.66813 28.8643 6.00073L11.8969 22.7852L6.39956 24.7744C5.70926 25.0255 5.03709 24.6999 4.90043 24.0502C4.85439 23.8145 4.87613 23.5707 4.96318 23.3466L7.01316 17.8617L23.9332 1.12279C25.2817 -0.209809 27.294 -0.382247 28.4278 0.740671ZM11.1564 2.40987C11.3395 2.40987 11.5208 2.44556 11.69 2.51488C11.8592 2.58421 12.0129 2.68582 12.1424 2.81392C12.2719 2.94202 12.3747 3.0941 12.4447 3.26147C12.5148 3.42883 12.5509 3.60822 12.5509 3.78938C12.5509 3.97054 12.5148 4.14992 12.4447 4.31729C12.3747 4.48466 12.2719 4.63674 12.1424 4.76484C12.0129 4.89294 11.8592 4.99455 11.69 5.06388C11.5208 5.1332 11.3395 5.16889 11.1564 5.16889H5.57817C4.83846 5.16889 4.12905 5.45957 3.60599 5.97698C3.08294 6.4944 2.78909 7.19616 2.78909 7.9279V24.482C2.78909 25.2137 3.08294 25.9155 3.60599 26.4329C4.12905 26.9503 4.83846 27.241 5.57817 27.241H22.3127C23.0524 27.241 23.7618 26.9503 24.2849 26.4329C24.8079 25.9155 25.1018 25.2137 25.1018 24.482V18.9639C25.1018 18.5981 25.2487 18.2472 25.5102 17.9885C25.7718 17.7298 26.1265 17.5844 26.4963 17.5844C26.8662 17.5844 27.2209 17.7298 27.4824 17.9885C27.744 18.2472 27.8909 18.5981 27.8909 18.9639V24.482C27.8909 25.9454 27.3032 27.349 26.2571 28.3838C25.211 29.4186 23.7921 30 22.3127 30H5.57817C4.09875 30 2.67992 29.4186 1.63381 28.3838C0.587699 27.349 0 25.9454 0 24.482V7.9279C0 6.46443 0.587699 5.0609 1.63381 4.02607C2.67992 2.99124 4.09875 2.40987 5.57817 2.40987H11.1564Z" fill={color} />
            </svg>
    </div>
    <div className="hidden md:flex">
        {SkriveeLogo(color,h,w,txt)}
    </div>
    </>
  )
}

export default Skrivee