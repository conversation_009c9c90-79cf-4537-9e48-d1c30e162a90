import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { logout } from '../../action/userActions'

function Logout({color,h,w,txt=null}) {
    const dispatch = useDispatch();
    const {userLoading} = useSelector((state)=>state.user);
    const handleLogout = () =>{
        dispatch(logout())
    }
  return (
    <>
    <div className="flex md:gap-3 rounded-md cursor-pointer transition-all" onClick={handleLogout}>
        {userLoading ? <>
        <div className="w-6 h-6 border-2 border-gray-300 border-t-[#4A99F8] rounded-full animate-spin" />
        </> : 
        (
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M15 0H0V30H15L11.25 26.25H3.75V3.75H11.25M11.25 11.25H18.75V3.75L30 15L18.75 26.25V18.75H11.25"
                    fill={color} />
            </svg>
            )
        }
        <span className="hidden md:block text-sm font-medium text-gray-800">{txt}</span>
        
    </div>
    </>
  )
}

export default Logout