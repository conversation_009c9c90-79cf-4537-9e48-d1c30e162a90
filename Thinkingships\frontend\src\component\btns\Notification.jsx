import React from 'react'

const notification = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M5 11.9867C4.99909 10.1154 5.64539 8.29023 6.84909 6.76491C8.05279 5.2396 9.75411 4.08985 11.7167 3.47542C11.6407 3.04657 11.6689 2.60797 11.7993 2.18989C11.9298 1.77181 12.1594 1.3842 12.4723 1.0538C12.7853 0.723404 13.1741 0.458085 13.6119 0.276151C14.0497 0.0942172 14.5262 0 15.0083 0C15.4905 0 15.9669 0.0942172 16.4048 0.276151C16.8426 0.458085 17.2314 0.723404 17.5443 1.0538C17.8572 1.3842 18.0869 1.77181 18.2173 2.18989C18.3478 2.60797 18.376 3.04657 18.3 3.47542C20.2594 4.09236 21.9572 5.24319 23.1577 6.76829C24.3582 8.2934 25.0021 10.1172 25 11.9867V20.9934L30 23.9956V25.4967H0V23.9956L5 20.9934V11.9867ZM18.3333 26.9978C18.3333 27.794 17.9821 28.5576 17.357 29.1207C16.7319 29.6837 15.8841 30 15 30C14.1159 30 13.2681 29.6837 12.643 29.1207C12.0179 28.5576 11.6667 27.794 11.6667 26.9978H18.3333Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}   

function Notification({color,h,w,txt=null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                        <path d="M5 11.9867C4.99909 10.1154 5.64539 8.29023 6.84909 6.76491C8.05279 5.2396 9.75411 4.08985 11.7167 3.47542C11.6407 3.04657 11.6689 2.60797 11.7993 2.18989C11.9298 1.77181 12.1594 1.3842 12.4723 1.0538C12.7853 0.723404 13.1741 0.458085 13.6119 0.276151C14.0497 0.0942172 14.5262 0 15.0083 0C15.4905 0 15.9669 0.0942172 16.4048 0.276151C16.8426 0.458085 17.2314 0.723404 17.5443 1.0538C17.8572 1.3842 18.0869 1.77181 18.2173 2.18989C18.3478 2.60797 18.376 3.04657 18.3 3.47542C20.2594 4.09236 21.9572 5.24319 23.1577 6.76829C24.3582 8.2934 25.0021 10.1172 25 11.9867V20.9934L30 23.9956V25.4967H0V23.9956L5 20.9934V11.9867ZM18.3333 26.9978C18.3333 27.794 17.9821 28.5576 17.357 29.1207C16.7319 29.6837 15.8841 30 15 30C14.1159 30 13.2681 29.6837 12.643 29.1207C12.0179 28.5576 11.6667 27.794 11.6667 26.9978H18.3333Z" fill={color} />
        </svg>
    </div>
    <div className="hidden md:flex">
        {notification(color,h,w,txt)}
    </div>
    </>
  )
}

export default Notification