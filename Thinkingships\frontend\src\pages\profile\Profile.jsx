import React, { useState } from 'react';
import '../../styles/profileStyle.css';

const Profile = () => {
  const [fullName, setFullName] = useState('');
  const [userName, setUserName] = useState('');
  const [bio, setBio] = useState('');
  const [passion, setPassion] = useState('');
  const [gender, setGender] = useState('');
  const [dob, setDob] = useState('');
  const [occupation, setOccupation] = useState('');
  const [mobile, setMobile] = useState('');
  const [location, setLocation] = useState('');
  const [language, setLanguage] = useState('');
  const [email, setEmail] = useState('');
  const [showMobile, setShowMobile] = useState(false);
  const [showEmail, setShowEmail] = useState(false);

  const renderInputField = (label, value, setValue, type = "text", required = false) => (
    <div className="form__group field w-full">
      <input
        type={type}
        className="form__field"
        placeholder={label}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        required={required}
      />
      <label className="form__label">{label}</label>
    </div>
  );

  return (
    <div className="w-full bg-gray-50 flex flex-col gap-6 p-6">
      {/* Full Name & Username */}
      <div className="flex flex-col md:flex-row gap-4">
        {renderInputField("Full Name", fullName, setFullName, "text", true)}
        {renderInputField("Username", userName, setUserName)}
      </div>

      {/* Bio */}
      <div className="w-full">
        {renderInputField("Bio", bio, setBio, "text", true)}
      </div>

      {/* Passion */}
      <div className="w-full">
        {renderInputField("Passion", passion, setPassion)}
      </div>

      {/* Gender */}
      <div className="w-full md:w-1/2">
        <div className="form__group field w-full">
          <select
            className="form__field"
            value={gender}
            onChange={(e) => setGender(e.target.value)}
          >
            <option value="" disabled>Select Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="others">Others</option>
          </select>
          <label className="form__label">Gender</label>
        </div>
      </div>

      {/* DOB & Occupation */}
      <div className="flex flex-col md:flex-row gap-4">
        {renderInputField("Date of Birth", dob, setDob, "date", true)}
        {renderInputField("Occupation", occupation, setOccupation)}
      </div>

      {/* Location & Mobile */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="form__group field w-full relative">
          <input
            type={showMobile ? "text" : "password"}
            className="form__field"
            placeholder="Mobile"
            value={mobile}
            onChange={(e) => setMobile(e.target.value)}
            required
          />
          <label className="form__label">Mobile</label>
          <button
            type="button"
            onClick={() => setShowMobile(!showMobile)}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-blue-500"
          >
            {showMobile ? "Hide" : "Show"}
          </button>
        </div>
        {renderInputField("Location", location, setLocation)}
      </div>

      {/* Language & Email */}
      <div className="flex flex-col md:flex-row gap-4">
        {renderInputField("Language", language, setLanguage)}
        <div className="form__group field w-full relative">
          <input
            type={showEmail ? "text" : "password"}
            className="form__field"
            placeholder="Email"
            value={email}
            disabled
          />
          <label className="form__label">Email</label>
          <button
            type="button"
            onClick={() => setShowEmail(!showEmail)}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-sm text-blue-500"
          >
            {showEmail ? "Hide" : "Show"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Profile;


