import axios from 'axios';
import { useState } from 'react';
import toast from 'react-hot-toast';
// import { fetchMe } from '../../../backend/controller/user.controller';
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;
const config_application_json = {
  headers: {
    // 'Content-Type': 'multiform-data',
  },
  withCredentials: true,
  timeout: import.meta.env.VITE_API_TIMEOUT
  // timeout set for 7s
}

function SkriveeHandling() {
  const [skriveeLoading, setSkriveeLoading] = useState(false);

  const createSkrivee = async (formData) => {
    try {
      setSkriveeLoading(true);
      const res = await axios.post(`${BACKEND_URL}/v1/skrivee/create`, formData ,config_application_json);
      return res?.data ?? {};
    } catch (error) {
      handleError(error);
      return {};
    } finally {
      setSkriveeLoading(false);
    }
  };

  const updateSkrivee = async (formData) => {
    try {
      setSkriveeLoading(true);
      const res = await axios.put(`${BACKEND_URL}/v1/skrivee/update`, formData, config_application_json);
      toast.success('Successfully Updated!',{duration:2500})
      return res?.data;
    } catch (error) {
      handleError(error);
      return null;
    } finally {
      setSkriveeLoading(false);
    }
  };

  const handleError = (error) => {
    if (error.code === 'ECONNABORTED') {
      toast.error("Timeout error");
    } else {
      toast.error(error?.response?.data?.err ?? "Something went wrong");
    }
  };

  return { createSkrivee, skriveeLoading, updateSkrivee }
}

export default SkriveeHandling