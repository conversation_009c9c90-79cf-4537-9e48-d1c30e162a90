import axios from 'axios';
import React from 'react'
import { useState } from 'react';
import toast from 'react-hot-toast';
import { useDispatch } from 'react-redux';
// import { fetchMe } from '../../../backend/controller/user.controller';
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL;
const config_application_json = {
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: import.meta.env.VITE_API_TIMEOUT
  // timeout set for 7s
}

function ProfileHandling() {
  const [profileLoading, setProfileLoading] = useState(false);
  const dispatch = useDispatch();
  const fetchProfile = async () => {
    try {
      setProfileLoading(true);
      const res = await axios.get(`${BACKEND_URL}/v1/user/fetchProfile`, config_application_json);
      return res?.data?.userProfile ?? {};
    } catch (error) {
      handleError(error);
      return {};
    } finally {
      setProfileLoading(false);
    }
  };

  const profileUpdate = async (formData) => {
    try {
      setProfileLoading(true);
      const res = await axios.put(`${BACKEND_URL}/v1/user/updateProfile`, formData, config_application_json);
      
      // dispatch(fetchMe())
      toast.success('Successfully Updated!',{duration:2500})
      return res?.data;
    } catch (error) {
      handleError(error);
      return null;
    } finally {
      setProfileLoading(false);
    }
  };

  const handleError = (error) => {
    if (error.code === 'ECONNABORTED') {
      toast.error("Timeout error");
    } else {
      toast.error(error?.response?.data?.err ?? "Something went wrong");
    }
  };

  return { fetchProfile, profileLoading, profileUpdate }
}

export default ProfileHandling