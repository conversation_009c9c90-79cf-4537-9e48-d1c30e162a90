import React, { useState } from 'react';
import EditHeader from './EditHeader';
import SocialLinks from './SocialLinks';
import PhoneInput from './PhoneInput';
import EmailVerification from './EmailVerification';
import { useEffect } from 'react';
import ProfileHandling from '../../../action/ProfileHandling';
function AuthorEdit({ user }) {
    const { fetchProfile, profileLoading, profileUpdate } = ProfileHandling();

    const [formData, setFormData] = useState({
        userName: user.userName,
        fullName: '',
        bio: '',
        passion: '',
        gender: '',
        customGender: '',
        dob: '',
        occupation: '',
        location: '',
        phone: '',
        userLanguage: '',
        // Optional: social links or hidden flags
        social_web_url: '',
        social_insta_url: '',
        social_fb_url: '',
        social_linkedin_url: '',
        social_twitter_url: '',
        phoneHidden: true,
        emailHidden: true,
        social_goodReads_url: ''
    });


    useEffect(() => {
        (async () => {
            const data = await fetchProfile();
            if (Object.keys(data).length > 0) {
                setFormData((prev) => ({
                    ...prev,
                    fullName: data.fullName ?? '',
                    bio: data.bio ?? '',
                    passion: data.passion ?? '',
                    gender: data.gender ?? '',
                    dob: data.dob ? new Date(data.dob).toISOString().split('T')[0] : '',
                    occupation: data.occupation ?? '',
                    location: data.location ?? '',
                    phone: data.phone ?? '',
                    language: data.userLanguage ?? '',
                    social_web_url: data.social_web_url ?? '',
                    social_insta_url: data.social_insta_url ?? '',
                    social_fb_url: data.social_fb_url ?? '',
                    social_linkedin_url: data.social_linkedin_url ?? '',
                    social_twitter_url: data.social_twitter_url ?? '',
                }));
            }
        })();
    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const isCustomGender = formData.gender === 'custom';
    const handleSubmit = (e) => {
        e.preventDefault();
        profileUpdate(formData)
    }
    console.log(formData)
    return (
        <div className="flex-1 w-full px-4 mb-10 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
            {
                profileLoading ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4A99F8]"></div>
                    </div>
                ) : (
                    <>
                        <EditHeader user={user.userName || ''} txt="Author" />
                        {!user.isVerified ? <EmailVerification email={user.email} /> : <></>}
                        <form className="space-y-8 mt-8 bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl md:p-12 md:px-12 p-6 border border-white/20 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-800/50" onSubmit={handleSubmit}>
                            {/* Full Name & Username */}
                            <div className="md:flex-row flex-col flex gap-6">
                                <div className="w-full md:w-1/2">
                                    <label className="block font-semibold text-gray-700 mb-2">Full Name <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="fullName" value={formData.fullName} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm" required />
                                </div>
                                <div className="w-full md:w-1/2">
                                    <label className="block font-semibold text-gray-700 mb-2">Username <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="userName" value={formData.userName} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm" required />
                                </div>
                            </div>
 
                            {/* Bio */}
                            <div>
                                <label className="block font-semibold text-gray-700 mb-2">Bio <span className="text-red-500">*</span></label>
                                <textarea
                                    autoComplete='off'
                                    name="bio"
                                    value={formData.bio}
                                    onChange={handleChange}
                                    maxLength={150}
                                    className={`textarea w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm resize-none ${formData.bio.length > 150 ? 'ring-red-500 border-red-300' : 'focus:ring-[#4A99F8] focus:border-transparent'}`}
                                    rows={4}
                                    placeholder="Tell us about yourself..."
                                ></textarea>
                                <div className={`text-sm text-right mt-1 font-medium ${formData.bio.length >= 150 ? 'text-red-500' : 'text-gray-500'}`}>
                                    {formData.bio.length}/150
                                </div>
                            </div>

                            {/* Passion */}
                            <div>
                                <label className="block font-semibold text-gray-700 mb-2">Passion</label>
                                <textarea
                                    name="passion"
                                    autoComplete='off'
                                    value={formData.passion}
                                    onChange={handleChange}
                                    maxLength={100}
                                    className="textarea w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm resize-none"
                                    rows={2}
                                    placeholder="What drives your creativity?"
                                ></textarea>
                                <div className={`text-sm text-right mt-1 font-medium ${formData.passion.length >= 100 ? 'text-red-500' : 'text-gray-500'}`}>
                                    {formData.passion.length}/100
                                </div>
                            </div>

                            {/* Gender Selection */}
                            <div>
                                <label className="block font-semibold text-gray-700 mb-3">Gender <span className="text-red-500">*</span></label>
                                <div className="flex items-center gap-6 flex-wrap">
                                    <label className="flex items-center gap-2 cursor-pointer hover:text-[#4A99F8] transition-colors duration-200">
                                        <input type="radio" name="gender" value="male" checked={formData.gender === 'male'} onChange={handleChange} className="w-4 h-4 text-[#4A99F8] border-gray-300 focus:ring-[#4A99F8]" />
                                        <span className="font-medium">Male</span>
                                    </label>
                                    <label className="flex items-center gap-2 cursor-pointer hover:text-[#4A99F8] transition-colors duration-200">
                                        <input type="radio" name="gender" value="female" checked={formData.gender === 'female'} onChange={handleChange} className="w-4 h-4 text-[#4A99F8] border-gray-300 focus:ring-[#4A99F8]" />
                                        <span className="font-medium">Female</span>
                                    </label>
                                    <label className="flex items-center gap-2 cursor-pointer hover:text-[#4A99F8] transition-colors duration-200">
                                        <input type="radio" name="gender" value="custom" checked={formData.gender === 'custom'} onChange={handleChange} className="w-4 h-4 text-[#4A99F8] border-gray-300 focus:ring-[#4A99F8]" />
                                        <span className="font-medium">Custom</span>
                                    </label>
                                    <input
                                        type="text"
                                        autoComplete='off'
                                        name="customGender"
                                        placeholder="Specify..."
                                        value={formData.customGender}
                                        onChange={handleChange}
                                        disabled={!isCustomGender}
                                        className="input border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed"
                                    />
                                </div>
                            </div>

                            {/* DOB & Occupation */}
                            <div className="flex gap-6 flex-col md:flex-row">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Date of Birth <span className="text-red-500">*</span></label>
                                    <input type="date" name="dob" value={formData.dob} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm" required />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Occupation</label>
                                    <input type="text" autoComplete='off'
                                        name="occupation" value={formData.occupation} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Your profession" />
                                </div>
                            </div>

                            {/* Location & Mobile */}
                            <div className="flex flex-col gap-6">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Location <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="location" value={formData.location} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Your city, country" required />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">
                                        Mobile Number <span className="text-red-500">*</span>
                                    </label>
                                    <div className="flex gap-3 items-end">
                                        <div className="flex-1 max-w-md">
                                            <PhoneInput
                                                value={formData.phone}
                                                onChange={(formattedPhone) =>
                                                    setFormData((prev) => ({ ...prev, mobile: formattedPhone }))
                                                }
                                                required
                                            />
                                        </div>
                                        <button
                                            type="button"
                                            className="bg-[#4A99F8] hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg hover:scale-105 active:scale-95"
                                        >
                                            Verify
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Email ID & Language */}
                            <div className="flex gap-6 md:flex-row flex-col">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Language</label>
                                    <input type="text" name="language" value={formData.language} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="e.g., English, Hindi" />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Email ID <span className="text-red-500">*</span></label>
                                    <div className="bg-gray-100 p-3 border border-gray-300 rounded-lg text-sm font-medium flex items-center text-gray-600 cursor-not-allowed">
                                        {user.email}
                                    </div>
                                </div>
                            </div>
                            <SocialLinks formData={formData} onChange={(socialData) => {
                                setFormData(prev => ({ ...prev, ...socialData }));
                            }} />

                            <div className="flex justify-center items-center pt-6 border-t border-gray-200">
                                <div className="flex items-center justify-center mt-6 gap-3">
                                    <div className="md:w-32 w-20 h-1 rounded-l-full bg-gradient-to-r from-[#4A99F8] to-blue-500 transform -translate-y-2" />
                                    <button
                                        type="submit"
                                        className="relative transform -translate-y-2 bg-gradient-to-r from-[#4A99F8] to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95"
                                    >
                                        Save Changes
                                    </button>
                                    <div className="md:w-32 w-20 h-1 rounded-r-full transform -translate-y-2 bg-gradient-to-r from-[#D99FE9] to-purple-400" />
                                </div>
                            </div>
                        </form>
        </>
    )
}
            </div >
    );
}

export default AuthorEdit;