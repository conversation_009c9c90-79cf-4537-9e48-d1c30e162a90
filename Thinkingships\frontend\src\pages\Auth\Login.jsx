import React, { useState } from 'react';
import { <PERSON>, EyeOff } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { login } from '../../action/userActions';
import { Toaster } from 'react-hot-toast';
import { FcGoogle } from 'react-icons/fc';
import { Link } from 'react-router-dom';

function Login() {
    const [showPass, setShowPass] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const dispatch = useDispatch();
    const { userLoading } = useSelector((state) => state.user);

    const handleSubmit = (e) => {
        e.preventDefault();
        dispatch(login({ email, password }));
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#F8F8F8] relative overflow-hidden px-4">
            {/* Background Bubbles */}
            <div className="absolute right-[-150px] bottom-[-50px] transform w-[400px] h-[400px] bg-blue-200 rounded-full z-0" />
            <div className="absolute left-[-100px] top-1 transform -translate-y-1/2 w-[400px] h-[400px] bg-blue-200 rounded-full z-0" />

            {/* Login Form */}
            <form
                onSubmit={handleSubmit}
                className="bg-white bg-opacity-90 backdrop-blur-md p-6 rounded-xl shadow-md z-10 w-full max-w-sm text-center mb-2"
            >
                <h2 className="text-2xl font-semibold mb-6">Welcome back</h2>

                <input
                    type="email"
                    placeholder="Email / User-name"
                    autoComplete="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-2 mb-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />

                <div className="relative mb-4">
                    <input
                        type={showPass ? 'text' : 'password'}
                        placeholder="Password"
                        autoComplete="current-password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-9"
                    />
                    <button
                        type="button"
                        onMouseDown={() => setShowPass(true)}
                        onMouseUp={() => setShowPass(false)}
                        onMouseLeave={() => setShowPass(false)}
                        className="absolute inset-y-0 right-3 flex items-center text-gray-500"
                    >
                        {!showPass ? <EyeOff size={18} /> : <Eye size={18} />}
                    </button>
                </div>

                {userLoading ? (
                    <button
                        type="button"
                        disabled
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md flex items-center justify-center gap-2 cursor-default opacity-75"
                    >
                        <svg
                            className="animate-spin h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            ></circle>
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                            ></path>
                        </svg>
                        Logging in...
                    </button>
                ) : (
                    <button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition duration-300"
                    >
                        Login
                    </button>
                )}

                {/* Divider */}
                <div className="my-6 flex items-center gap-2">
                    <div className="flex-grow h-px bg-gray-300" />
                    <span className="text-sm text-gray-500">or login with</span>
                    <div className="flex-grow h-px bg-gray-300" />
                </div>

                {/* Google Login */}
                <div className="flex justify-center">
                    <div className="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-full cursor-pointer hover:bg-gray-100 transition">
                        <FcGoogle size={36} />
                    </div>
                </div>
            </form>

            {/* Signup Link */}
            <div className="bg-white bg-opacity-90 backdrop-blur-md p-4 rounded-xl shadow-md z-10 w-full max-w-sm text-center ">
                <p className="text-sm text-gray-600">
                    Don’t have an account?{' '}
                    <Link to="/" className="text-blue-600 hover:underline font-medium">
                        Signup
                    </Link>
                </p>
            </div>

            <Toaster position="top-center" reverseOrder={false} />
        </div>
    );
}

export default Login;
