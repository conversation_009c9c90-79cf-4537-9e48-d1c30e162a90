const express = require('express');
const { isAuthenticated } = require('../middleware/authentication');
const { saveOrPublishSkrivee, getSingleSkrivee, updateSkrivee, deleteSkrivee } = require('../controller/skrivee.controller');
const { validateFileSize , upload } = require('../utils/fileHandling');
const router = express.Router();




router.route('/skrivee/create').post(isAuthenticated,upload.single('story_cover'),validateFileSize,saveOrPublishSkrivee);
router.route('/skrivee/find/:id').get(isAuthenticated,getSingleSkrivee);
router.route('/skrivee/update/:id').put(isAuthenticated,updateSkrivee);
router.route('/skrivee/delete/:id').delete(isAuthenticated,deleteSkrivee);


module.exports = router;