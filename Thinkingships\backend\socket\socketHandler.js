const {io} = require('../app')


// mapping users -> userId (key) -> socketId (value)
let onlineUsers = {};

// socket handler
io.on("connect", (socket) =>{
    console.log("a user is connected to the socket : ",socket.id);
    const userId = socket.handshake.query.userId;

    // addition of userId to the map
    if(userId) onlineUsers[userId] = socket.id;

    // emitting all the online users (passing userId to the frontend)
    io.emit('getOnlineUsers',Object.keys(onlineUsers));

    // handling disconnection and removal of online user once they are disconnected
    socket.on("disconnect",()=>{
        console.log("a user just got disconnected : ",socket.id);
        delete onlineUsers[userId]
        io.emit('getOnlineUsers',Object.keys(onlineUsers));
    })
})

// returns socketId based on userId
const getReceiverSocket = (userId) => onlineUsers[userId];

module.exports = {
    getReceiverSocket
}