const catchAsyncError = require("../middleware/catchAsyncError");
const jwt = require('jsonwebtoken')
const getJWTToken = (user) =>{
    return jwt.sign({id:user.id},process.env.JWT_SECRET,{
        expiresIn:process.env.JWT_EXPIRE
    })
}

exports.sendToken = catchAsyncError(async(res,user,statusCode)=>{
    const token = getJWTToken(user);

    const options = {
        // Total Days * day * hour * mins * ms 
        expires: new Date(Date.now() + process.env.COOKIE_EXPIRE * 24 * 60 * 60 * 1000),
        httpOnly: process.env.ENV_TYPE !== 'dev',
        secure: process.env.ENV_TYPE !== 'dev',
        sameSite: process.env.ENV_TYPE === 'dev' ? 'lax' : 'none'
    }
    res.cookie('token',token,options); //setting up the cookies
    res.status(200).json({
        success:true,
        user,
        token
    })
})