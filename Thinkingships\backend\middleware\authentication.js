const pool  = require("../config/sqlConnector");
const ErrorHandler = require("../errors/ErrorHandler");
const catchAsyncError = require("./catchAsyncError");
const jwt = require('jsonwebtoken')

exports.isAuthenticated = catchAsyncError(async (req, res, next) => {
    const jwtToken =
        req.cookies.token ||
        (req.headers.authorization?.startsWith("Bearer ")
            ? req.headers.authorization.slice(7)
            : null);
    if (!jwtToken) {
        return next(new Error<PERSON>andler("Please login to access this resource", 401));
    }

    let decodeData;
    try {
        decodeData = jwt.verify(jwtToken, process.env.JWT_SECRET);
    } catch (err) {
        return next(new ErrorHandler("Invalid or expired token", 401));
    }

    const [rows] = await pool.query(
        `SELECT id, email, userName, userRole, isVerified, isProfileComplete ,createdAt, updatedAt
        FROM users
        WHERE isDeleted = FALSE AND id = ?`,
        [decodeData.id]
    );

    const user = rows[0];
    if (!user) {
        return next(new ErrorHandler("No user found", 404));
    }
    req.user = user;
    next();
});

exports.authRole = (...allowedRoles) => (req, res, next) => {
    if (!allowedRoles.includes(req.user.userRole)) {
        return next(new ErrorHandler("Not authorized", 403));
    }
    next();
};
