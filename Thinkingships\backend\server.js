//env setup
const dotenv = require('dotenv')
dotenv.config({path:__dirname+'/.env'})


// env requirements
if (!process.env.ENV_TYPE || !process.env.BACKEND_URL || !process.env.BACKEND_PORT) {
    console.error("Missing essential environment variables, Shutting down server.");
    process.exit(1);
}

// imports
const {app, server, io} = require('./app');
const error = require('./errors/error');

// initializing
require('./socket/socketHandler')
require('./config/sqlConnector')

// starting the server
server.listen(process.env.BACKEND_PORT,'0.0.0.0',()=>{
    console.log(`server started running on : ${process.env.BACKEND_URL}`)
})

app.get('/',(req,res)=>{
    try{
        res.status(200).json({
        message: "Strivee backend is running",
        uptime: process.uptime().toFixed(2) + 's'
        })
    }catch(err){
        console.log(err)
    } 
    
})

// Route imports
const userRoutes = require('./routes/user.route')
const skriveeRoutes = require('./routes/skrivee.route')
const rankRoutes = require('./routes/rank.route')
// Routes handling
app.use(`/v1`,userRoutes);
app.use(`/v1`,skriveeRoutes)
app.use(`/v1`,rankRoutes)

// setting up error handler middleware 
app.use(error)

// log for success
console.log("server started for :",process.env.ENV_TYPE);