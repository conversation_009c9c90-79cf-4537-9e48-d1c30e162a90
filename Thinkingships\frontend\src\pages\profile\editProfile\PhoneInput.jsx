import React, { useState, useEffect } from 'react';
import Select, { components } from 'react-select';
import countries from './countries';
import { FiSearch } from 'react-icons/fi';

function PhoneInput({ value = '', onChange, error }) {
  const countryCodes = countries.map(c => c.dialCode);
  const defaultCountry = countries[0];

  // Parse country code and number from the incoming value
  const extractCountryAndNumber = (full) => {
    const trimmed = full.trim();
    const matchedCode = countryCodes.find(code => trimmed.startsWith(code));
    
    if (matchedCode) {
      return {
        code: matchedCode,
        number: trimmed.slice(matchedCode.length).trim()
      };
    }
    return { code: defaultCountry.dialCode, number: '' };
  };

  const { code, number } = extractCountryAndNumber(value);
    
  const [selectedCountry, setSelectedCountry] = useState(
    countries.find(c => c.dialCode === code) || defaultCountry
  );
  const [phone, setPhone] = useState(number);
  // Options for dropdown
  const options = countries.map((c) => ({
    value: c.dialCode,
    label: `${c.name} (${c.dialCode})`,
    code: c.code,
    name: c.name,
    dialCode: c.dialCode,
    flag: c.flag,
    country: c,
  }));

  // Display selected flag and code
  const customSingleValue = ({ data }) => (
    <div className="flex items-center gap-2">
      <img src={data.flag} alt={data.code} className="w-5 h-4 object-cover rounded-sm" />
      <span>{data.dialCode}</span>
    </div>
  );

  // Custom dropdown items
  const customOption = ({ data, innerRef, innerProps }) => (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center gap-2 px-2 py-1 hover:bg-gray-100 cursor-pointer"
    >
      <img src={data.flag} alt={data.code} className="w-5 h-4 object-cover rounded-sm" />
      <span>{data.name} ({data.dialCode})</span>
    </div>
  );

  // Handle dropdown change
  const handleSelect = (selectedOption) => {
    const country = selectedOption.country;
    setSelectedCountry(country);
    onChange(`${country.dialCode} ${phone}`); // re-emit
  };

  // Handle typing in number input
  const handlePhoneChange = (e) => {
    const input = e.target.value.replace(/\D/g, '');
    setPhone(input);
    onChange(`${selectedCountry.dialCode} ${input}`);
  };

  return (
    <div className="w-full">
      <div className="flex gap-2">
        <div className="md:w-1/4 w-1/2">
          <Select
            options={options}
            value={options.find(o => o.value === selectedCountry.dialCode)}
            onChange={handleSelect}
            isSearchable={false}
            components={{
              SingleValue: customSingleValue,
              Option: customOption,
              Input: () => null,
              MenuList: (props) => (
                <components.MenuList {...props} className="!pt-0 !pb-0" />
              ),
            }}
            className="text-sm"
            styles={{
              control: (base) => ({
                ...base,
                padding: '0px',
                minHeight: '44px',
              }),
              menuList: (base) => ({
                ...base,
                paddingTop: 0,
                paddingBottom: 0,
              }),
            }}
          />
        </div>
        <input
          type="tel"
          name="phone"
          placeholder="XXXXXXXXXX"
          value={phone}
          onChange={handlePhoneChange}
          required
          className="flex-1 md:w-3/4 w-1/2 border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 bg-white focus:ring-[#4A99F8]"
        />
      </div>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

export default PhoneInput;
